import 'package:flutter/material.dart';
import '../../../domain/entities/sync_status.dart';

/// Widget for displaying sync status indicators
class SyncStatusWidget extends StatelessWidget {
  final SyncStatus syncStatus;
  final bool showLabel;
  final double iconSize;

  const SyncStatusWidget({
    Key? key,
    required this.syncStatus,
    this.showLabel = true,
    this.iconSize = 16.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          _getIcon(),
          color: _getColor(context),
          size: iconSize,
        ),
        if (showLabel) ...[
          const SizedBox(width: 4),
          Text(
            syncStatus.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getColor(context),
            ),
          ),
        ],
      ],
    );
  }

  IconData _getIcon() {
    switch (syncStatus) {
      case SyncStatus.local:
        return Icons.cloud_off;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.cloud_done;
      case SyncStatus.error:
        return Icons.error_outline;
    }
  }

  Color _getColor(BuildContext context) {
    switch (syncStatus) {
      case SyncStatus.local:
        return Colors.orange;
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }
}
