import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/presentation/providers/sync_provider.dart';
import 'package:safestride/domain/usecases/sync_data.dart';
import 'package:safestride/domain/usecases/resolve_sync_conflict.dart';
import 'package:safestride/services/sync/sync_service.dart';
import 'package:safestride/services/sync/connectivity_service.dart';
import 'package:safestride/domain/entities/sync_conflict.dart';

import 'sync_provider_test.mocks.dart';

@GenerateMocks([
  SyncDataUseCase,
  ResolveSyncConflictUseCase,
  SyncService,
  ConnectivityService,
])
void main() {
  late SyncProvider syncProvider;
  late MockSyncDataUseCase mockSyncDataUseCase;
  late MockResolveSyncConflictUseCase mockResolveSyncConflictUseCase;
  late MockSyncService mockSyncService;
  late MockConnectivityService mockConnectivityService;
  late StreamController<SyncServiceEvent> syncEventController;
  late StreamController<bool> connectivityController;

  setUp(() {
    mockSyncDataUseCase = MockSyncDataUseCase();
    mockResolveSyncConflictUseCase = MockResolveSyncConflictUseCase();
    mockSyncService = MockSyncService();
    mockConnectivityService = MockConnectivityService();
    syncEventController = StreamController<SyncServiceEvent>.broadcast();
    connectivityController = StreamController<bool>.broadcast();

    when(mockSyncService.events).thenAnswer((_) => syncEventController.stream);
    when(mockSyncService.initialize()).thenAnswer((_) async {});
    when(mockSyncService.isAutoSyncEnabled).thenReturn(true);
    when(mockSyncService.dispose()).thenReturn(null);

    when(mockConnectivityService.connectivityStream)
        .thenAnswer((_) => connectivityController.stream);
    when(mockConnectivityService.initialize()).thenAnswer((_) async {});
    when(mockConnectivityService.isConnected).thenReturn(true);
    when(mockConnectivityService.dispose()).thenReturn(null);

    syncProvider = SyncProvider(
      syncDataUseCase: mockSyncDataUseCase,
      resolveSyncConflictUseCase: mockResolveSyncConflictUseCase,
      syncService: mockSyncService,
      connectivityService: mockConnectivityService,
    );
  });

  tearDown(() {
    syncEventController.close();
    connectivityController.close();
    syncProvider.dispose();
  });

  group('SyncProvider', () {
    test('should initialize successfully', () async {
      // Arrange
      final mockStatus = SyncStatusResult(
        statistics: SyncStatistics(
          totalEntities: 10,
          syncedEntities: 8,
          pendingEntities: 2,
          errorEntities: 0,
          conflictCount: 0,
        ),
      );

      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => mockStatus);
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      // Act
      await syncProvider.initialize();

      // Assert
      expect(syncProvider.isConnected, true);
      expect(syncProvider.isAutoSyncEnabled, true);
      expect(syncProvider.syncStatistics?.totalEntities, 10);
      verify(mockSyncService.initialize()).called(1);
      verify(mockConnectivityService.initialize()).called(1);
    });

    test('should trigger sync successfully', () async {
      // Arrange
      final mockResult = SyncResult(
        success: true,
        duration: Duration(seconds: 5),
        statistics: SyncStatistics(
          totalEntities: 5,
          syncedEntities: 5,
          pendingEntities: 0,
          errorEntities: 0,
          conflictCount: 0,
        ),
      );

      when(mockSyncService.triggerSync(syncType: SyncType.full))
          .thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      await syncProvider.initialize();

      // Act
      await syncProvider.triggerSync();

      // Assert
      expect(syncProvider.lastSyncResult?.success, true);
      expect(syncProvider.syncError, null);
      verify(mockSyncService.triggerSync(syncType: SyncType.full)).called(1);
    });

    test('should handle sync failure', () async {
      // Arrange
      final mockResult = SyncResult(
        success: false,
        error: 'Network error',
        duration: Duration(seconds: 2),
      );

      when(mockSyncService.triggerSync(syncType: SyncType.full))
          .thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      await syncProvider.initialize();

      // Act
      await syncProvider.triggerSync();

      // Assert
      expect(syncProvider.lastSyncResult?.success, false);
      expect(syncProvider.syncError, 'Network error');
    });

    test('should force sync', () async {
      // Arrange
      final mockResult = SyncResult(success: true, duration: Duration(seconds: 3));

      when(mockSyncService.forcSync()).thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      await syncProvider.initialize();

      // Act
      await syncProvider.forceSync();

      // Assert
      expect(syncProvider.lastSyncResult?.success, true);
      verify(mockSyncService.forcSync()).called(1);
    });

    test('should cancel sync', () async {
      // Arrange
      when(mockSyncService.cancelSync()).thenAnswer((_) async {});
      await syncProvider.initialize();

      // Act
      await syncProvider.cancelSync();

      // Assert
      expect(syncProvider.isSyncing, false);
      verify(mockSyncService.cancelSync()).called(1);
    });

    test('should toggle auto sync', () async {
      // Arrange
      when(mockSyncService.setAutoSyncEnabled(false)).thenReturn(null);
      await syncProvider.initialize();

      // Act
      syncProvider.setAutoSyncEnabled(false);

      // Assert
      expect(syncProvider.isAutoSyncEnabled, false);
      verify(mockSyncService.setAutoSyncEnabled(false)).called(1);
    });

    test('should resolve conflict successfully', () async {
      // Arrange
      final conflict = SyncConflict(
        id: 'conflict1',
        entityType: SyncEntityType.walkabout,
        entityId: 'walkabout1',
        localData: {'title': 'Local Title'},
        remoteData: {'title': 'Remote Title'},
        conflictType: SyncConflictType.updateConflict,
        createdAt: DateTime.now(),
      );

      final mockResult = ConflictResolutionResult(
        success: true,
        resolvedConflict: conflict,
      );

      when(mockResolveSyncConflictUseCase.call(any)).thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => [conflict]);

      await syncProvider.initialize();

      // Act
      final result = await syncProvider.resolveConflict('conflict1', SyncConflictResolution.useLocal);

      // Assert
      expect(result, true);
      expect(syncProvider.conflicts.length, 0); // Conflict should be removed
      verify(mockResolveSyncConflictUseCase.call(any)).called(1);
    });

    test('should handle conflict resolution failure', () async {
      // Arrange
      final mockResult = ConflictResolutionResult(
        success: false,
        error: 'Resolution failed',
      );

      when(mockResolveSyncConflictUseCase.call(any)).thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      await syncProvider.initialize();

      // Act
      final result = await syncProvider.resolveConflict('conflict1', SyncConflictResolution.useLocal);

      // Assert
      expect(result, false);
      expect(syncProvider.syncError, 'Resolution failed');
    });

    test('should resolve multiple conflicts', () async {
      // Arrange
      final conflicts = [
        SyncConflict(
          id: 'conflict1',
          entityType: SyncEntityType.walkabout,
          entityId: 'walkabout1',
          localData: {'title': 'Local Title 1'},
          remoteData: {'title': 'Remote Title 1'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
        SyncConflict(
          id: 'conflict2',
          entityType: SyncEntityType.hazard,
          entityId: 'hazard1',
          localData: {'severity': 'high'},
          remoteData: {'severity': 'medium'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
      ];

      final mockResult = BatchConflictResolutionResult(
        results: [
          ConflictResolutionResult(success: true, resolvedConflict: conflicts[0]),
          ConflictResolutionResult(success: true, resolvedConflict: conflicts[1]),
        ],
        successCount: 2,
        failureCount: 0,
        totalCount: 2,
      );

      when(mockResolveSyncConflictUseCase.resolveMultipleConflicts(any, any))
          .thenAnswer((_) async => mockResult);
      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => conflicts);

      await syncProvider.initialize();

      // Act
      final result = await syncProvider.resolveMultipleConflicts(
        ['conflict1', 'conflict2'],
        SyncConflictResolution.useRemote,
      );

      // Assert
      expect(result.allSuccessful, true);
      expect(result.successCount, 2);
      expect(syncProvider.conflicts.length, 0); // All conflicts should be removed
    });

    test('should handle sync service events', () async {
      // Arrange
      await syncProvider.initialize();

      // Act
      syncEventController.add(SyncServiceEvent.syncStarted(SyncType.full));
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(syncProvider.isSyncing, true);
      expect(syncProvider.syncProgress, 0.0);

      // Act
      syncEventController.add(SyncServiceEvent.syncCompleted(
        SyncStatistics(
          totalEntities: 10,
          syncedEntities: 10,
          pendingEntities: 0,
          errorEntities: 0,
          conflictCount: 0,
        ),
        Duration(seconds: 5),
      ));
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(syncProvider.isSyncing, false);
      expect(syncProvider.syncProgress, 1.0);
      expect(syncProvider.lastSyncTime, isNotNull);
    });

    test('should handle connectivity changes', () async {
      // Arrange
      await syncProvider.initialize();

      // Act
      connectivityController.add(false);
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(syncProvider.isConnected, false);

      // Act
      connectivityController.add(true);
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(syncProvider.isConnected, true);
    });

    test('should refresh sync status', () async {
      // Arrange
      final mockStatus = SyncStatusResult(
        statistics: SyncStatistics(
          totalEntities: 15,
          syncedEntities: 10,
          pendingEntities: 5,
          errorEntities: 0,
          conflictCount: 2,
        ),
      );

      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => mockStatus);
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => []);

      await syncProvider.initialize();

      // Act
      await syncProvider.refreshSyncStatus();

      // Assert
      expect(syncProvider.syncStatistics?.totalEntities, 15);
      expect(syncProvider.syncStatistics?.pendingEntities, 5);
    });

    test('should get conflicts for entity', () async {
      // Arrange
      final conflicts = [
        SyncConflict(
          id: 'conflict1',
          entityType: SyncEntityType.walkabout,
          entityId: 'walkabout1',
          localData: {'title': 'Local Title'},
          remoteData: {'title': 'Remote Title'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
        SyncConflict(
          id: 'conflict2',
          entityType: SyncEntityType.hazard,
          entityId: 'hazard1',
          localData: {'severity': 'high'},
          remoteData: {'severity': 'medium'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
      ];

      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => SyncStatusResult());
      when(mockResolveSyncConflictUseCase.getUnresolvedConflicts())
          .thenAnswer((_) async => conflicts);

      await syncProvider.initialize();

      // Act
      final walkaboutConflicts = syncProvider.getConflictsForEntity(
        'walkabout1',
        SyncEntityType.walkabout,
      );
      final hasConflicts = syncProvider.hasConflictsForEntity(
        'walkabout1',
        SyncEntityType.walkabout,
      );

      // Assert
      expect(walkaboutConflicts.length, 1);
      expect(walkaboutConflicts.first.entityId, 'walkabout1');
      expect(hasConflicts, true);
    });

    test('should dispose resources properly', () {
      // Act
      syncProvider.dispose();

      // Assert
      verify(mockSyncService.dispose()).called(1);
      verify(mockConnectivityService.dispose()).called(1);
    });
  });
}
