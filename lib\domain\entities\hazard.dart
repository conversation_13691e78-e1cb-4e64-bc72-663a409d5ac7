import 'walkabout.dart';
import 'sync_status.dart';

/// Hazard entity representing a safety hazard documented during walkabouts
///
/// This entity follows Clean Architecture principles and contains
/// the core business logic for hazard management.
class Hazard {
  final String id;
  final String walkaboutId;
  final String title;
  final String? description;
  final HazardSeverity severity;
  final HazardCategory category;
  final GeoPoint? location;
  final List<String> photos;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final SyncStatus syncStatus;

  const Hazard({
    required this.id,
    required this.walkaboutId,
    required this.title,
    this.description,
    required this.severity,
    required this.category,
    this.location,
    required this.photos,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    required this.syncStatus,
  });

  /// Create a copy of this hazard with updated fields
  Hazard copyWith({
    String? id,
    String? walkaboutId,
    String? title,
    String? description,
    HazardSeverity? severity,
    HazardCategory? category,
    GeoPoint? location,
    List<String>? photos,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Hazard(
      id: id ?? this.id,
      walkaboutId: walkaboutId ?? this.walkaboutId,
      title: title ?? this.title,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      category: category ?? this.category,
      location: location ?? this.location,
      photos: photos ?? List<String>.from(this.photos),
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Hazard &&
        other.id == id &&
        other.walkaboutId == walkaboutId &&
        other.title == title &&
        other.description == description &&
        other.severity == severity &&
        other.category == category &&
        other.location == location &&
        _listEquals(other.photos, photos) &&
        other.notes == notes &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.syncStatus == syncStatus;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      walkaboutId,
      title,
      description,
      severity,
      category,
      location,
      Object.hashAll(photos),
      notes,
      createdAt,
      updatedAt,
      syncStatus,
    );
  }

  @override
  String toString() {
    return 'Hazard(id: $id, walkaboutId: $walkaboutId, title: $title, '
        'description: $description, severity: $severity, category: $category, '
        'location: $location, photos: $photos, notes: $notes, '
        'createdAt: $createdAt, updatedAt: $updatedAt, syncStatus: $syncStatus)';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}

/// Hazard severity enumeration
enum HazardSeverity {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case HazardSeverity.low:
        return 'Low';
      case HazardSeverity.medium:
        return 'Medium';
      case HazardSeverity.high:
        return 'High';
      case HazardSeverity.critical:
        return 'Critical';
    }
  }

  String get description {
    switch (this) {
      case HazardSeverity.low:
        return 'Minor risk with minimal impact';
      case HazardSeverity.medium:
        return 'Moderate risk requiring attention';
      case HazardSeverity.high:
        return 'Significant risk requiring immediate action';
      case HazardSeverity.critical:
        return 'Severe risk requiring urgent intervention';
    }
  }

  /// Color representation for UI
  int get colorValue {
    switch (this) {
      case HazardSeverity.low:
        return 0xFF4CAF50; // Green
      case HazardSeverity.medium:
        return 0xFFFF9800; // Orange
      case HazardSeverity.high:
        return 0xFFFF5722; // Deep Orange
      case HazardSeverity.critical:
        return 0xFFF44336; // Red
    }
  }
}

/// Hazard category enumeration
enum HazardCategory {
  slipTripFall,
  electrical,
  chemical,
  fire,
  machinery,
  ergonomic,
  environmental,
  biological,
  radiation,
  other;

  String get displayName {
    switch (this) {
      case HazardCategory.slipTripFall:
        return 'Slip/Trip/Fall';
      case HazardCategory.electrical:
        return 'Electrical';
      case HazardCategory.chemical:
        return 'Chemical';
      case HazardCategory.fire:
        return 'Fire';
      case HazardCategory.machinery:
        return 'Machinery';
      case HazardCategory.ergonomic:
        return 'Ergonomic';
      case HazardCategory.environmental:
        return 'Environmental';
      case HazardCategory.biological:
        return 'Biological';
      case HazardCategory.radiation:
        return 'Radiation';
      case HazardCategory.other:
        return 'Other';
    }
  }

  String get description {
    switch (this) {
      case HazardCategory.slipTripFall:
        return 'Hazards related to slipping, tripping, or falling';
      case HazardCategory.electrical:
        return 'Electrical hazards and equipment safety';
      case HazardCategory.chemical:
        return 'Chemical exposure and handling hazards';
      case HazardCategory.fire:
        return 'Fire and explosion hazards';
      case HazardCategory.machinery:
        return 'Machinery and equipment hazards';
      case HazardCategory.ergonomic:
        return 'Ergonomic and repetitive strain hazards';
      case HazardCategory.environmental:
        return 'Environmental and weather-related hazards';
      case HazardCategory.biological:
        return 'Biological and infectious hazards';
      case HazardCategory.radiation:
        return 'Radiation and electromagnetic hazards';
      case HazardCategory.other:
        return 'Other unspecified hazards';
    }
  }

  /// Icon representation for UI
  String get iconName {
    switch (this) {
      case HazardCategory.slipTripFall:
        return 'warning';
      case HazardCategory.electrical:
        return 'flash_on';
      case HazardCategory.chemical:
        return 'science';
      case HazardCategory.fire:
        return 'local_fire_department';
      case HazardCategory.machinery:
        return 'precision_manufacturing';
      case HazardCategory.ergonomic:
        return 'accessibility';
      case HazardCategory.environmental:
        return 'eco';
      case HazardCategory.biological:
        return 'biotech';
      case HazardCategory.radiation:
        return 'radioactive';
      case HazardCategory.other:
        return 'help_outline';
    }
  }
}
