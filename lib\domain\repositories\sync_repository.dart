import '../entities/sync_conflict.dart';
import '../entities/sync_status.dart';

/// Repository interface for sync operations
///
/// This repository handles synchronization between local and remote data sources
/// following Clean Architecture principles.
abstract class SyncRepository {
  /// Sync all pending data to remote
  /// Returns true if sync was successful, false otherwise
  Future<bool> syncToRemote();

  /// Sync data from remote to local
  /// Returns true if sync was successful, false otherwise
  Future<bool> syncFromRemote();

  /// Perform bidirectional sync
  /// Returns true if sync was successful, false otherwise
  Future<bool> performFullSync();

  /// Get all unresolved sync conflicts
  Future<List<SyncConflict>> getUnresolvedConflicts();

  /// Resolve a sync conflict
  /// Returns true if resolution was successful
  Future<bool> resolveConflict(String conflictId, SyncConflictResolution resolution);

  /// Get sync status for all entities
  Future<Map<String, SyncStatus>> getSyncStatuses();

  /// Get entities that need to be synced
  Future<List<String>> getEntitiesNeedingSync();

  /// Mark entity as synced
  Future<void> markEntityAsSynced(String entityId, SyncEntityType entityType);

  /// Mark entity as sync error
  Future<void> markEntityAsSyncError(String entityId, SyncEntityType entityType, String error);

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime();

  /// Update last sync timestamp
  Future<void> updateLastSyncTime(DateTime timestamp);

  /// Check if sync is currently in progress
  Future<bool> isSyncInProgress();

  /// Cancel ongoing sync operation
  Future<void> cancelSync();

  /// Get sync statistics
  Future<SyncStatistics> getSyncStatistics();
}

/// Sync statistics data class
class SyncStatistics {
  final int totalEntities;
  final int syncedEntities;
  final int pendingEntities;
  final int errorEntities;
  final int conflictCount;
  final DateTime? lastSyncTime;
  final Duration? lastSyncDuration;

  const SyncStatistics({
    required this.totalEntities,
    required this.syncedEntities,
    required this.pendingEntities,
    required this.errorEntities,
    required this.conflictCount,
    this.lastSyncTime,
    this.lastSyncDuration,
  });

  /// Calculate sync progress as percentage
  double get syncProgress {
    if (totalEntities == 0) return 1.0;
    return syncedEntities / totalEntities;
  }

  /// Check if all entities are synced
  bool get isFullySynced => pendingEntities == 0 && errorEntities == 0;

  /// Check if there are any sync issues
  bool get hasSyncIssues => errorEntities > 0 || conflictCount > 0;
}
