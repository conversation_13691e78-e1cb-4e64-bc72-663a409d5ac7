import '../../domain/entities/sync_conflict.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/repositories/sync_repository.dart';
import '../../domain/repositories/walkabout_repository.dart';
import '../../domain/repositories/hazard_repository.dart';
import '../datasources/local/sync_local_datasource.dart';
import '../datasources/remote/sync_remote_datasource.dart';

/// Implementation of SyncRepository
class SyncRepositoryImpl implements SyncRepository {
  final SyncLocalDataSource _localDataSource;
  final SyncRemoteDataSource _remoteDataSource;
  final WalkaboutRepository _walkaboutRepository;
  final HazardRepository _hazardRepository;

  bool _isSyncInProgress = false;

  SyncRepositoryImpl({
    required SyncLocalDataSource localDataSource,
    required SyncRemoteDataSource remoteDataSource,
    required WalkaboutRepository walkaboutRepository,
    required HazardRepository hazardRepository,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource,
        _walkaboutRepository = walkaboutRepository,
        _hazardRepository = hazardRepository;

  @override
  Future<bool> syncToRemote() async {
    if (_isSyncInProgress) return false;
    
    _isSyncInProgress = true;
    
    try {
      final entitiesNeedingSync = await _localDataSource.getEntitiesNeedingSync();
      bool allSuccessful = true;

      for (final entity in entitiesNeedingSync) {
        bool success = false;
        
        try {
          // Update status to syncing
          await _localDataSource.updateEntitySyncStatus(
            entity.id,
            entity.type,
            SyncStatus.syncing,
          );

          if (entity.type == SyncEntityType.walkabout) {
            final walkabout = await _walkaboutRepository.getWalkaboutById(entity.id);
            if (walkabout != null) {
              success = await _remoteDataSource.uploadWalkabout(walkabout);
            }
          } else if (entity.type == SyncEntityType.hazard) {
            final hazard = await _hazardRepository.getHazardById(entity.id);
            if (hazard != null) {
              success = await _remoteDataSource.uploadHazard(hazard);
            }
          }

          // Update sync status based on result
          final newStatus = success ? SyncStatus.synced : SyncStatus.error;
          await _localDataSource.updateEntitySyncStatus(
            entity.id,
            entity.type,
            newStatus,
          );

          if (!success) {
            allSuccessful = false;
          }
        } catch (e) {
          await _localDataSource.updateEntitySyncStatus(
            entity.id,
            entity.type,
            SyncStatus.error,
          );
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } finally {
      _isSyncInProgress = false;
    }
  }

  @override
  Future<bool> syncFromRemote() async {
    if (_isSyncInProgress) return false;
    
    _isSyncInProgress = true;
    
    try {
      final lastSyncTime = await _localDataSource.getLastSyncTime();
      
      // Get current user ID (this would typically come from auth service)
      // For now, we'll need to get it from the first walkabout or pass it as parameter
      final localWalkabouts = await _walkaboutRepository.getAllWalkabouts();
      if (localWalkabouts.isEmpty) return true; // No data to sync
      
      final userId = localWalkabouts.first.userId;
      
      // Download walkabouts
      final remoteWalkabouts = await _remoteDataSource.downloadWalkabouts(userId, lastSyncTime);
      
      for (final remoteWalkabout in remoteWalkabouts) {
        try {
          final localWalkabout = await _walkaboutRepository.getWalkaboutById(remoteWalkabout.id);
          
          if (localWalkabout == null) {
            // New walkabout from remote
            await _walkaboutRepository.createWalkabout(remoteWalkabout.copyWith(
              syncStatus: SyncStatus.synced,
            ));
          } else {
            // Check for conflicts
            if (localWalkabout.updatedAt.isAfter(remoteWalkabout.updatedAt) &&
                localWalkabout.syncStatus == SyncStatus.local) {
              // Conflict detected
              await _createSyncConflict(
                localWalkabout.id,
                SyncEntityType.walkabout,
                localWalkabout.toJson(),
                remoteWalkabout.toJson(),
                SyncConflictType.updateConflict,
              );
            } else {
              // Update with remote version
              await _walkaboutRepository.updateWalkabout(remoteWalkabout.copyWith(
                syncStatus: SyncStatus.synced,
              ));
            }
          }
        } catch (e) {
          // Handle individual walkabout sync errors
          continue;
        }
      }

      // Download hazards for each walkabout
      for (final walkabout in localWalkabouts) {
        final remoteHazards = await _remoteDataSource.downloadHazards(walkabout.id, lastSyncTime);
        
        for (final remoteHazard in remoteHazards) {
          try {
            final localHazard = await _hazardRepository.getHazardById(remoteHazard.id);
            
            if (localHazard == null) {
              // New hazard from remote
              await _hazardRepository.createHazard(remoteHazard.copyWith(
                syncStatus: SyncStatus.synced,
              ));
            } else {
              // Check for conflicts
              if (localHazard.updatedAt.isAfter(remoteHazard.updatedAt) &&
                  localHazard.syncStatus == SyncStatus.local) {
                // Conflict detected
                await _createSyncConflict(
                  localHazard.id,
                  SyncEntityType.hazard,
                  localHazard.toJson(),
                  remoteHazard.toJson(),
                  SyncConflictType.updateConflict,
                );
              } else {
                // Update with remote version
                await _hazardRepository.updateHazard(remoteHazard.copyWith(
                  syncStatus: SyncStatus.synced,
                ));
              }
            }
          } catch (e) {
            // Handle individual hazard sync errors
            continue;
          }
        }
      }

      // Update last sync time
      await _localDataSource.updateLastSyncTime(DateTime.now());
      
      return true;
    } finally {
      _isSyncInProgress = false;
    }
  }

  @override
  Future<bool> performFullSync() async {
    if (_isSyncInProgress) return false;
    
    try {
      // First sync to remote, then from remote
      final toRemoteSuccess = await syncToRemote();
      final fromRemoteSuccess = await syncFromRemote();
      
      return toRemoteSuccess && fromRemoteSuccess;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<SyncConflict>> getUnresolvedConflicts() async {
    return await _localDataSource.getUnresolvedConflicts();
  }

  @override
  Future<bool> resolveConflict(String conflictId, SyncConflictResolution resolution) async {
    try {
      await _localDataSource.resolveConflict(conflictId, resolution);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Map<String, SyncStatus>> getSyncStatuses() async {
    final stats = await _localDataSource.getSyncStatistics();
    final statuses = <String, SyncStatus>{};
    
    for (final entry in stats.entries) {
      if (entry.key.startsWith('walkabout_') || entry.key.startsWith('hazard_')) {
        final parts = entry.key.split('_');
        if (parts.length == 2) {
          final statusName = parts[1];
          try {
            statuses[entry.key] = SyncStatus.values.byName(statusName);
          } catch (e) {
            // Invalid status name, skip
          }
        }
      }
    }
    
    return statuses;
  }

  @override
  Future<List<String>> getEntitiesNeedingSync() async {
    final entities = await _localDataSource.getEntitiesNeedingSync();
    return entities.map((e) => '${e.type.name}:${e.id}').toList();
  }

  @override
  Future<void> markEntityAsSynced(String entityId, SyncEntityType entityType) async {
    await _localDataSource.updateEntitySyncStatus(entityId, entityType, SyncStatus.synced);
  }

  @override
  Future<void> markEntityAsSyncError(String entityId, SyncEntityType entityType, String error) async {
    await _localDataSource.updateEntitySyncStatus(entityId, entityType, SyncStatus.error);
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    return await _localDataSource.getLastSyncTime();
  }

  @override
  Future<void> updateLastSyncTime(DateTime timestamp) async {
    await _localDataSource.updateLastSyncTime(timestamp);
  }

  @override
  Future<bool> isSyncInProgress() async {
    return _isSyncInProgress;
  }

  @override
  Future<void> cancelSync() async {
    _isSyncInProgress = false;
  }

  @override
  Future<SyncStatistics> getSyncStatistics() async {
    final stats = await _localDataSource.getSyncStatistics();
    
    int totalEntities = 0;
    int syncedEntities = 0;
    int pendingEntities = 0;
    int errorEntities = 0;
    
    for (final entry in stats.entries) {
      if (entry.key.contains('_local') || entry.key.contains('_error')) {
        pendingEntities += entry.value;
        totalEntities += entry.value;
      } else if (entry.key.contains('_synced')) {
        syncedEntities += entry.value;
        totalEntities += entry.value;
      } else if (entry.key.contains('_syncing')) {
        totalEntities += entry.value;
      } else if (entry.key.contains('_error')) {
        errorEntities += entry.value;
      }
    }
    
    final conflictCount = stats['conflicts'] ?? 0;
    final lastSyncTime = await getLastSyncTime();
    
    return SyncStatistics(
      totalEntities: totalEntities,
      syncedEntities: syncedEntities,
      pendingEntities: pendingEntities,
      errorEntities: errorEntities,
      conflictCount: conflictCount,
      lastSyncTime: lastSyncTime,
    );
  }

  /// Helper method to create sync conflicts
  Future<void> _createSyncConflict(
    String entityId,
    SyncEntityType entityType,
    Map<String, dynamic> localData,
    Map<String, dynamic> remoteData,
    SyncConflictType conflictType,
  ) async {
    final conflict = SyncConflict(
      id: '${entityType.name}_${entityId}_${DateTime.now().millisecondsSinceEpoch}',
      entityType: entityType,
      entityId: entityId,
      localData: localData,
      remoteData: remoteData,
      conflictType: conflictType,
      createdAt: DateTime.now(),
    );
    
    await _localDataSource.storeSyncConflict(conflict);
  }
}

/// Extension to add toJson method to entities
extension WalkaboutJson on Walkabout {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status.name,
      'location': location != null ? {
        'latitude': location!.latitude,
        'longitude': location!.longitude,
      } : null,
      'userId': userId,
      'isCompleted': isCompleted,
      'syncStatus': syncStatus.name,
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }
}

extension HazardJson on Hazard {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'walkaboutId': walkaboutId,
      'title': title,
      'description': description,
      'severity': severity.name,
      'category': category.name,
      'location': location != null ? {
        'latitude': location!.latitude,
        'longitude': location!.longitude,
      } : null,
      'photos': photos,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncStatus': syncStatus.name,
    };
  }
}
