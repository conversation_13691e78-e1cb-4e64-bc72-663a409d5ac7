import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../domain/entities/sync_conflict.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/usecases/sync_data.dart';
import '../../domain/usecases/resolve_sync_conflict.dart';
import '../../services/sync/sync_service.dart';
import '../../services/sync/connectivity_service.dart';

/// Provider for sync state management
///
/// Handles sync operations, conflict resolution, and UI state updates
/// following Clean Architecture and Provider pattern principles.
class SyncProvider extends ChangeNotifier {
  final SyncDataUseCase _syncDataUseCase;
  final ResolveSyncConflictUseCase _resolveSyncConflictUseCase;
  final SyncService _syncService;
  final ConnectivityService _connectivityService;

  StreamSubscription<SyncServiceEvent>? _syncEventSubscription;
  StreamSubscription<bool>? _connectivitySubscription;

  // State variables
  bool _isSyncing = false;
  bool _isConnected = false;
  bool _isAutoSyncEnabled = true;
  SyncResult? _lastSyncResult;
  List<SyncConflict> _conflicts = [];
  SyncStatistics? _syncStatistics;
  String? _syncError;
  DateTime? _lastSyncTime;
  double _syncProgress = 0.0;
  int _retryAttempts = 0;

  SyncProvider({
    required SyncDataUseCase syncDataUseCase,
    required ResolveSyncConflictUseCase resolveSyncConflictUseCase,
    required SyncService syncService,
    required ConnectivityService connectivityService,
  })  : _syncDataUseCase = syncDataUseCase,
        _resolveSyncConflictUseCase = resolveSyncConflictUseCase,
        _syncService = syncService,
        _connectivityService = connectivityService;

  // Getters
  bool get isSyncing => _isSyncing;
  bool get isConnected => _isConnected;
  bool get isAutoSyncEnabled => _isAutoSyncEnabled;
  SyncResult? get lastSyncResult => _lastSyncResult;
  List<SyncConflict> get conflicts => List.unmodifiable(_conflicts);
  SyncStatistics? get syncStatistics => _syncStatistics;
  String? get syncError => _syncError;
  DateTime? get lastSyncTime => _lastSyncTime;
  double get syncProgress => _syncProgress;
  int get retryAttempts => _retryAttempts;

  /// Check if there are unresolved conflicts
  bool get hasConflicts => _conflicts.isNotEmpty;

  /// Get conflict count
  int get conflictCount => _conflicts.length;

  /// Check if sync is needed
  bool get needsSync => _syncStatistics?.pendingEntities ?? 0 > 0;

  /// Check if there are sync errors
  bool get hasSyncErrors => _syncStatistics?.errorEntities ?? 0 > 0;

  /// Initialize the sync provider
  Future<void> initialize() async {
    try {
      await _syncService.initialize();
      await _connectivityService.initialize();
      
      _isConnected = _connectivityService.isConnected;
      _isAutoSyncEnabled = _syncService.isAutoSyncEnabled;
      
      _startListening();
      await _refreshSyncStatus();
      
      notifyListeners();
    } catch (e) {
      _syncError = 'Failed to initialize sync: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Manually trigger sync
  Future<void> triggerSync({SyncType syncType = SyncType.full}) async {
    try {
      _clearError();
      _isSyncing = true;
      notifyListeners();

      final result = await _syncService.triggerSync(syncType: syncType);
      _lastSyncResult = result;
      
      if (!result.success) {
        _syncError = result.error;
      }
      
      await _refreshSyncStatus();
    } catch (e) {
      _syncError = 'Sync failed: ${e.toString()}';
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Force sync regardless of connectivity
  Future<void> forceSync() async {
    try {
      _clearError();
      _isSyncing = true;
      notifyListeners();

      final result = await _syncService.forcSync();
      _lastSyncResult = result;
      
      if (!result.success) {
        _syncError = result.error;
      }
      
      await _refreshSyncStatus();
    } catch (e) {
      _syncError = 'Force sync failed: ${e.toString()}';
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Cancel ongoing sync
  Future<void> cancelSync() async {
    try {
      await _syncService.cancelSync();
      _isSyncing = false;
      notifyListeners();
    } catch (e) {
      _syncError = 'Failed to cancel sync: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Enable or disable auto sync
  void setAutoSyncEnabled(bool enabled) {
    _syncService.setAutoSyncEnabled(enabled);
    _isAutoSyncEnabled = enabled;
    notifyListeners();
  }

  /// Resolve a sync conflict
  Future<bool> resolveConflict(String conflictId, SyncConflictResolution resolution) async {
    try {
      _clearError();
      
      final result = await _resolveSyncConflictUseCase.call(
        ConflictResolutionParams(
          conflictId: conflictId,
          resolution: resolution,
        ),
      );
      
      if (result.success) {
        // Remove resolved conflict from local list
        _conflicts.removeWhere((c) => c.id == conflictId);
        await _refreshSyncStatus();
        notifyListeners();
        return true;
      } else {
        _syncError = result.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _syncError = 'Failed to resolve conflict: ${e.toString()}';
      notifyListeners();
      return false;
    }
  }

  /// Resolve multiple conflicts with the same resolution
  Future<BatchConflictResolutionResult> resolveMultipleConflicts(
    List<String> conflictIds,
    SyncConflictResolution resolution,
  ) async {
    try {
      _clearError();
      
      final result = await _resolveSyncConflictUseCase.resolveMultipleConflicts(
        conflictIds,
        resolution,
      );
      
      // Remove successfully resolved conflicts
      for (final conflictResult in result.results) {
        if (conflictResult.success && conflictResult.resolvedConflict != null) {
          _conflicts.removeWhere((c) => c.id == conflictResult.resolvedConflict!.id);
        }
      }
      
      await _refreshSyncStatus();
      notifyListeners();
      
      return result;
    } catch (e) {
      _syncError = 'Failed to resolve conflicts: ${e.toString()}';
      notifyListeners();
      
      return BatchConflictResolutionResult(
        results: [],
        successCount: 0,
        failureCount: conflictIds.length,
        totalCount: conflictIds.length,
      );
    }
  }

  /// Refresh sync status and conflicts
  Future<void> refreshSyncStatus() async {
    await _refreshSyncStatus();
    notifyListeners();
  }

  /// Get conflicts for a specific entity
  List<SyncConflict> getConflictsForEntity(String entityId, SyncEntityType entityType) {
    return _conflicts
        .where((c) => c.entityId == entityId && c.entityType == entityType)
        .toList();
  }

  /// Check if entity has conflicts
  bool hasConflictsForEntity(String entityId, SyncEntityType entityType) {
    return _conflicts.any((c) => c.entityId == entityId && c.entityType == entityType);
  }

  @override
  void dispose() {
    _syncEventSubscription?.cancel();
    _connectivitySubscription?.cancel();
    _syncService.dispose();
    _connectivityService.dispose();
    super.dispose();
  }

  /// Start listening to sync and connectivity events
  void _startListening() {
    // Listen to sync service events
    _syncEventSubscription = _syncService.events.listen(_handleSyncEvent);
    
    // Listen to connectivity changes
    _connectivitySubscription = _connectivityService.connectivityStream.listen(
      (isConnected) {
        _isConnected = isConnected;
        notifyListeners();
      },
    );
  }

  /// Handle sync service events
  void _handleSyncEvent(SyncServiceEvent event) {
    switch (event.type) {
      case SyncServiceEventType.syncStarted:
        _isSyncing = true;
        _syncProgress = 0.0;
        _clearError();
        break;
        
      case SyncServiceEventType.syncCompleted:
        _isSyncing = false;
        _syncProgress = 1.0;
        _lastSyncTime = event.timestamp;
        _syncStatistics = event.data['statistics'] as SyncStatistics?;
        _lastSyncResult = SyncResult(
          success: true,
          statistics: _syncStatistics,
          duration: event.data['duration'] as Duration,
        );
        break;
        
      case SyncServiceEventType.syncFailed:
        _isSyncing = false;
        _syncProgress = 0.0;
        _syncError = event.data['error'] as String?;
        _lastSyncResult = SyncResult(
          success: false,
          error: _syncError,
          duration: event.data['duration'] as Duration,
        );
        break;
        
      case SyncServiceEventType.syncCancelled:
        _isSyncing = false;
        _syncProgress = 0.0;
        break;
        
      case SyncServiceEventType.retryAttempt:
        _retryAttempts = event.data['attempt'] as int;
        break;
        
      case SyncServiceEventType.maxRetriesReached:
        _syncError = 'Max retry attempts reached';
        break;
        
      case SyncServiceEventType.autoSyncEnabled:
        _isAutoSyncEnabled = true;
        break;
        
      case SyncServiceEventType.autoSyncDisabled:
        _isAutoSyncEnabled = false;
        break;
        
      default:
        break;
    }
    
    notifyListeners();
  }

  /// Refresh sync status from use case
  Future<void> _refreshSyncStatus() async {
    try {
      final statusResult = await _syncDataUseCase.getSyncStatus();
      _syncStatistics = statusResult.statistics;
      
      if (statusResult.error != null) {
        _syncError = statusResult.error;
      }
      
      // Refresh conflicts
      _conflicts = await _resolveSyncConflictUseCase.getUnresolvedConflicts();
      
      // Update last sync time from statistics
      _lastSyncTime = _syncStatistics?.lastSyncTime;
      
    } catch (e) {
      _syncError = 'Failed to refresh sync status: ${e.toString()}';
    }
  }

  /// Clear sync error
  void _clearError() {
    _syncError = null;
  }
}
