import 'sync_status.dart';

/// Walkabout entity representing a safety inspection session
///
/// This entity follows Clean Architecture principles and contains
/// the core business logic for walkabout management.
class Walkabout {
  final String id;
  final String title;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final WalkaboutStatus status;
  final GeoPoint? location;
  final String userId;
  final bool isCompleted;
  final SyncStatus syncStatus;
  final DateTime? deletedAt;

  const Walkabout({
    required this.id,
    required this.title,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    this.location,
    required this.userId,
    required this.isCompleted,
    required this.syncStatus,
    this.deletedAt,
  });

  /// Create a copy of this walkabout with updated fields
  Walkabout copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    WalkaboutStatus? status,
    GeoPoint? location,
    String? userId,
    bool? isCompleted,
    SyncStatus? syncStatus,
    DateTime? deletedAt,
  }) {
    return Walkabout(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      location: location ?? this.location,
      userId: userId ?? this.userId,
      isCompleted: isCompleted ?? this.isCompleted,
      syncStatus: syncStatus ?? this.syncStatus,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Walkabout &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.status == status &&
        other.location == location &&
        other.userId == userId &&
        other.isCompleted == isCompleted &&
        other.syncStatus == syncStatus &&
        other.deletedAt == deletedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      createdAt,
      updatedAt,
      status,
      location,
      userId,
      isCompleted,
      syncStatus,
      deletedAt,
    );
  }

  @override
  String toString() {
    return 'Walkabout(id: $id, title: $title, description: $description, '
        'createdAt: $createdAt, updatedAt: $updatedAt, status: $status, '
        'location: $location, userId: $userId, isCompleted: $isCompleted, '
        'syncStatus: $syncStatus, deletedAt: $deletedAt)';
  }

  /// Check if this walkabout is soft deleted
  bool get isDeleted => deletedAt != null;
}

/// Walkabout status enumeration
enum WalkaboutStatus {
  draft,
  inProgress,
  completed,
  archived;

  String get displayName {
    switch (this) {
      case WalkaboutStatus.draft:
        return 'Draft';
      case WalkaboutStatus.inProgress:
        return 'In Progress';
      case WalkaboutStatus.completed:
        return 'Completed';
      case WalkaboutStatus.archived:
        return 'Archived';
    }
  }

  String get description {
    switch (this) {
      case WalkaboutStatus.draft:
        return 'Walkabout is being prepared';
      case WalkaboutStatus.inProgress:
        return 'Walkabout is currently active';
      case WalkaboutStatus.completed:
        return 'Walkabout has been finished';
      case WalkaboutStatus.archived:
        return 'Walkabout has been archived';
    }
  }
}

/// Geographic point representation
class GeoPoint {
  final double latitude;
  final double longitude;

  const GeoPoint({required this.latitude, required this.longitude});

  /// Create a copy of this GeoPoint with updated coordinates
  GeoPoint copyWith({double? latitude, double? longitude}) {
    return GeoPoint(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GeoPoint &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode => Object.hash(latitude, longitude);

  @override
  String toString() => 'GeoPoint(latitude: $latitude, longitude: $longitude)';
}
