import 'package:flutter/material.dart';
import '../../../domain/entities/sync_conflict.dart';

/// Dialog for resolving sync conflicts
class SyncConflictDialog extends StatefulWidget {
  final SyncConflict conflict;
  final Function(SyncConflictResolution) onResolve;

  const SyncConflictDialog({
    Key? key,
    required this.conflict,
    required this.onResolve,
  }) : super(key: key);

  @override
  State<SyncConflictDialog> createState() => _SyncConflictDialogState();
}

class _SyncConflictDialogState extends State<SyncConflictDialog> {
  SyncConflictResolution? _selectedResolution;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Sync Conflict'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Conflict Type: ${widget.conflict.conflictType.displayName}'),
          const SizedBox(height: 16),
          Text('Entity: ${widget.conflict.entityType.displayName}'),
          const SizedBox(height: 16),
          Text('Choose resolution:'),
          const SizedBox(height: 8),
          ...SyncConflictResolution.values.map((resolution) => 
            RadioListTile<SyncConflictResolution>(
              title: Text(resolution.displayName),
              subtitle: Text(resolution.description),
              value: resolution,
              groupValue: _selectedResolution,
              onChanged: (value) {
                setState(() {
                  _selectedResolution = value;
                });
              },
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedResolution != null
              ? () {
                  widget.onResolve(_selectedResolution!);
                  Navigator.of(context).pop();
                }
              : null,
          child: const Text('Resolve'),
        ),
      ],
    );
  }
}
