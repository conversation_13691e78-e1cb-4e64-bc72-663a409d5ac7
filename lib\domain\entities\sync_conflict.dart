/// Sync conflict entity representing conflicts during data synchronization
///
/// This entity follows Clean Architecture principles and contains
/// the core business logic for sync conflict management.
class SyncConflict {
  final String id;
  final SyncEntityType entityType;
  final String entityId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final SyncConflictType conflictType;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final SyncConflictResolution? resolution;

  const SyncConflict({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.localData,
    required this.remoteData,
    required this.conflictType,
    required this.createdAt,
    this.resolvedAt,
    this.resolution,
  });

  /// Create a copy of this sync conflict with updated fields
  SyncConflict copyWith({
    String? id,
    SyncEntityType? entityType,
    String? entityId,
    Map<String, dynamic>? localData,
    Map<String, dynamic>? remoteData,
    SyncConflictType? conflictType,
    DateTime? createdAt,
    DateTime? resolvedAt,
    SyncConflictResolution? resolution,
  }) {
    return SyncConflict(
      id: id ?? this.id,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      localData: localData ?? this.localData,
      remoteData: remoteData ?? this.remoteData,
      conflictType: conflictType ?? this.conflictType,
      createdAt: createdAt ?? this.createdAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      resolution: resolution ?? this.resolution,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SyncConflict &&
        other.id == id &&
        other.entityType == entityType &&
        other.entityId == entityId &&
        _mapEquals(other.localData, localData) &&
        _mapEquals(other.remoteData, remoteData) &&
        other.conflictType == conflictType &&
        other.createdAt == createdAt &&
        other.resolvedAt == resolvedAt &&
        other.resolution == resolution;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      entityType,
      entityId,
      localData,
      remoteData,
      conflictType,
      createdAt,
      resolvedAt,
      resolution,
    );
  }

  @override
  String toString() {
    return 'SyncConflict(id: $id, entityType: $entityType, entityId: $entityId, '
        'localData: $localData, remoteData: $remoteData, conflictType: $conflictType, '
        'createdAt: $createdAt, resolvedAt: $resolvedAt, resolution: $resolution)';
  }

  /// Check if this conflict is resolved
  bool get isResolved => resolvedAt != null && resolution != null;

  /// Helper method to compare maps
  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}

/// Entity types that can have sync conflicts
enum SyncEntityType {
  walkabout,
  hazard;

  String get displayName {
    switch (this) {
      case SyncEntityType.walkabout:
        return 'Walkabout';
      case SyncEntityType.hazard:
        return 'Hazard';
    }
  }
}

/// Types of sync conflicts
enum SyncConflictType {
  updateConflict,
  deleteConflict,
  createConflict;

  String get displayName {
    switch (this) {
      case SyncConflictType.updateConflict:
        return 'Update Conflict';
      case SyncConflictType.deleteConflict:
        return 'Delete Conflict';
      case SyncConflictType.createConflict:
        return 'Create Conflict';
    }
  }

  String get description {
    switch (this) {
      case SyncConflictType.updateConflict:
        return 'Both local and remote versions were modified';
      case SyncConflictType.deleteConflict:
        return 'Item was deleted remotely but modified locally';
      case SyncConflictType.createConflict:
        return 'Item was created in both local and remote with same ID';
    }
  }
}

/// Resolution strategies for sync conflicts
enum SyncConflictResolution {
  useLocal,
  useRemote,
  merge;

  String get displayName {
    switch (this) {
      case SyncConflictResolution.useLocal:
        return 'Use Local Version';
      case SyncConflictResolution.useRemote:
        return 'Use Remote Version';
      case SyncConflictResolution.merge:
        return 'Merge Changes';
    }
  }

  String get description {
    switch (this) {
      case SyncConflictResolution.useLocal:
        return 'Keep the local changes and discard remote changes';
      case SyncConflictResolution.useRemote:
        return 'Keep the remote changes and discard local changes';
      case SyncConflictResolution.merge:
        return 'Combine both local and remote changes';
    }
  }
}
