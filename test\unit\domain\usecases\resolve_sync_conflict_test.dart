import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/repositories/sync_repository.dart';
import 'package:safestride/domain/usecases/resolve_sync_conflict.dart';
import 'package:safestride/domain/entities/sync_conflict.dart';

import 'resolve_sync_conflict_test.mocks.dart';

@GenerateMocks([SyncRepository])
void main() {
  late ResolveSyncConflictUseCase useCase;
  late MockSyncRepository mockRepository;

  setUp(() {
    mockRepository = MockSyncRepository();
    useCase = ResolveSyncConflictUseCase(mockRepository);
  });

  group('ResolveSyncConflictUseCase', () {
    final testConflict = SyncConflict(
      id: 'conflict1',
      entityType: SyncEntityType.walkabout,
      entityId: 'walkabout1',
      localData: {'title': 'Local Title', 'description': 'Local Description'},
      remoteData: {'title': 'Remote Title', 'description': 'Remote Description'},
      conflictType: SyncConflictType.updateConflict,
      createdAt: DateTime.now(),
    );

    test('should resolve conflict successfully', () async {
      // Arrange
      final params = ConflictResolutionParams(
        conflictId: 'conflict1',
        resolution: SyncConflictResolution.useLocal,
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [testConflict]);
      when(mockRepository.resolveConflict('conflict1', SyncConflictResolution.useLocal))
          .thenAnswer((_) async => true);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, true);
      expect(result.error, null);
      expect(result.resolvedConflict?.id, 'conflict1');
      expect(result.resolvedConflict?.resolution, SyncConflictResolution.useLocal);
      expect(result.resolvedConflict?.resolvedAt, isNotNull);
      verify(mockRepository.resolveConflict('conflict1', SyncConflictResolution.useLocal))
          .called(1);
    });

    test('should return error for empty conflict ID', () async {
      // Arrange
      final params = ConflictResolutionParams(
        conflictId: '',
        resolution: SyncConflictResolution.useLocal,
      );

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, false);
      expect(result.error, 'Conflict ID cannot be empty');
      verifyNever(mockRepository.getUnresolvedConflicts());
    });

    test('should return error for non-existent conflict', () async {
      // Arrange
      final params = ConflictResolutionParams(
        conflictId: 'nonexistent',
        resolution: SyncConflictResolution.useLocal,
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [testConflict]);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, false);
      expect(result.error, 'Conflict not found or already resolved');
      verifyNever(mockRepository.resolveConflict(any, any));
    });

    test('should handle resolution failure', () async {
      // Arrange
      final params = ConflictResolutionParams(
        conflictId: 'conflict1',
        resolution: SyncConflictResolution.useRemote,
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [testConflict]);
      when(mockRepository.resolveConflict('conflict1', SyncConflictResolution.useRemote))
          .thenAnswer((_) async => false);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, false);
      expect(result.error, 'Failed to resolve conflict');
    });

    test('should handle repository exception', () async {
      // Arrange
      final params = ConflictResolutionParams(
        conflictId: 'conflict1',
        resolution: SyncConflictResolution.merge,
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenThrow(Exception('Database error'));

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, false);
      expect(result.error, contains('Database error'));
    });

    test('should get unresolved conflicts', () async {
      // Arrange
      final conflicts = [testConflict];
      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => conflicts);

      // Act
      final result = await useCase.getUnresolvedConflicts();

      // Assert
      expect(result, conflicts);
      verify(mockRepository.getUnresolvedConflicts()).called(1);
    });

    test('should handle exception when getting unresolved conflicts', () async {
      // Arrange
      when(mockRepository.getUnresolvedConflicts())
          .thenThrow(Exception('Database error'));

      // Act
      final result = await useCase.getUnresolvedConflicts();

      // Assert
      expect(result, isEmpty);
    });

    test('should get conflicts for specific entity', () async {
      // Arrange
      final walkaboutConflict = testConflict;
      final hazardConflict = SyncConflict(
        id: 'conflict2',
        entityType: SyncEntityType.hazard,
        entityId: 'hazard1',
        localData: {'severity': 'high'},
        remoteData: {'severity': 'medium'},
        conflictType: SyncConflictType.updateConflict,
        createdAt: DateTime.now(),
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [walkaboutConflict, hazardConflict]);

      // Act
      final result = await useCase.getConflictsForEntity(
        'walkabout1',
        SyncEntityType.walkabout,
      );

      // Assert
      expect(result.length, 1);
      expect(result.first.entityId, 'walkabout1');
      expect(result.first.entityType, SyncEntityType.walkabout);
    });

    test('should resolve multiple conflicts successfully', () async {
      // Arrange
      final conflict2 = SyncConflict(
        id: 'conflict2',
        entityType: SyncEntityType.hazard,
        entityId: 'hazard1',
        localData: {'severity': 'high'},
        remoteData: {'severity': 'medium'},
        conflictType: SyncConflictType.updateConflict,
        createdAt: DateTime.now(),
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [testConflict, conflict2]);
      when(mockRepository.resolveConflict('conflict1', SyncConflictResolution.useLocal))
          .thenAnswer((_) async => true);
      when(mockRepository.resolveConflict('conflict2', SyncConflictResolution.useLocal))
          .thenAnswer((_) async => true);

      // Act
      final result = await useCase.resolveMultipleConflicts(
        ['conflict1', 'conflict2'],
        SyncConflictResolution.useLocal,
      );

      // Assert
      expect(result.totalCount, 2);
      expect(result.successCount, 2);
      expect(result.failureCount, 0);
      expect(result.allSuccessful, true);
      expect(result.successRate, 1.0);
    });

    test('should handle partial failure in batch resolution', () async {
      // Arrange
      final conflict2 = SyncConflict(
        id: 'conflict2',
        entityType: SyncEntityType.hazard,
        entityId: 'hazard1',
        localData: {'severity': 'high'},
        remoteData: {'severity': 'medium'},
        conflictType: SyncConflictType.updateConflict,
        createdAt: DateTime.now(),
      );

      when(mockRepository.getUnresolvedConflicts())
          .thenAnswer((_) async => [testConflict, conflict2]);
      when(mockRepository.resolveConflict('conflict1', SyncConflictResolution.useRemote))
          .thenAnswer((_) async => true);
      when(mockRepository.resolveConflict('conflict2', SyncConflictResolution.useRemote))
          .thenAnswer((_) async => false);

      // Act
      final result = await useCase.resolveMultipleConflicts(
        ['conflict1', 'conflict2'],
        SyncConflictResolution.useRemote,
      );

      // Assert
      expect(result.totalCount, 2);
      expect(result.successCount, 1);
      expect(result.failureCount, 1);
      expect(result.allSuccessful, false);
      expect(result.successRate, 0.5);
    });
  });

  group('ConflictResolutionResult', () {
    test('should identify successful resolution', () {
      // Arrange
      final result = ConflictResolutionResult(success: true);

      // Act & Assert
      expect(result.isSuccessful, true);
    });

    test('should identify failed resolution', () {
      // Arrange
      final result = ConflictResolutionResult(
        success: false,
        error: 'Resolution failed',
      );

      // Act & Assert
      expect(result.isSuccessful, false);
    });
  });
}
