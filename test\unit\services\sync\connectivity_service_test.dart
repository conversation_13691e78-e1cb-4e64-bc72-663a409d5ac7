import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:safestride/services/sync/connectivity_service.dart';

import 'connectivity_service_test.mocks.dart';

@GenerateMocks([Connectivity])
void main() {
  late ConnectivityService connectivityService;
  late MockConnectivity mockConnectivity;
  late StreamController<ConnectivityResult> connectivityController;

  setUp(() {
    mockConnectivity = MockConnectivity();
    connectivityController = StreamController<ConnectivityResult>.broadcast();
    
    when(mockConnectivity.onConnectivityChanged)
        .thenAnswer((_) => connectivityController.stream);
    when(mockConnectivity.checkConnectivity())
        .thenAnswer((_) async => ConnectivityResult.wifi);

    connectivityService = ConnectivityService(connectivity: mockConnectivity);
  });

  tearDown(() {
    connectivityController.close();
    connectivityService.dispose();
  });

  group('ConnectivityService', () {
    test('should initialize successfully', () async {
      // Act
      await connectivityService.initialize();

      // Assert
      expect(connectivityService.isInitialized, true);
      verify(mockConnectivity.checkConnectivity()).called(1);
    });

    test('should detect WiFi connection', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => ConnectivityResult.wifi);

      // Act
      await connectivityService.initialize();

      // Assert
      expect(connectivityService.lastResult, ConnectivityResult.wifi);
      verify(mockConnectivity.checkConnectivity()).called(1);
    });

    test('should detect mobile connection', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => ConnectivityResult.mobile);

      // Act
      await connectivityService.initialize();

      // Assert
      expect(connectivityService.lastResult, ConnectivityResult.mobile);
    });

    test('should detect no connection', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => ConnectivityResult.none);

      // Act
      await connectivityService.initialize();

      // Assert
      expect(connectivityService.lastResult, ConnectivityResult.none);
      expect(connectivityService.isConnected, false);
    });

    test('should emit connectivity changes', () async {
      // Arrange
      await connectivityService.initialize();
      final connectivityChanges = <bool>[];
      connectivityService.connectivityStream.listen(connectivityChanges.add);

      // Act
      connectivityController.add(ConnectivityResult.wifi);
      connectivityController.add(ConnectivityResult.none);
      connectivityController.add(ConnectivityResult.mobile);

      // Wait for stream processing
      await Future.delayed(Duration(milliseconds: 50));

      // Assert
      expect(connectivityChanges.length, greaterThan(0));
    });

    test('should manually check connectivity', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => ConnectivityResult.ethernet);
      await connectivityService.initialize();

      // Act
      final isConnected = await connectivityService.checkConnectivity();

      // Assert
      expect(isConnected, isA<bool>());
      expect(connectivityService.lastResult, ConnectivityResult.ethernet);
    });

    test('should get detailed connectivity info', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => ConnectivityResult.wifi);
      await connectivityService.initialize();

      // Act
      final info = await connectivityService.getConnectivityInfo();

      // Assert
      expect(info.result, ConnectivityResult.wifi);
      expect(info.connectionType, 'WiFi');
      expect(info.timestamp, isA<DateTime>());
    });

    test('should handle connectivity errors gracefully', () async {
      // Arrange
      when(mockConnectivity.checkConnectivity())
          .thenThrow(Exception('Network error'));

      // Act
      await connectivityService.initialize();

      // Assert
      expect(connectivityService.isConnected, false);
      expect(connectivityService.lastResult, ConnectivityResult.none);
    });

    test('should not initialize twice', () async {
      // Act
      await connectivityService.initialize();
      await connectivityService.initialize();

      // Assert
      verify(mockConnectivity.checkConnectivity()).called(1);
    });

    test('should dispose resources properly', () {
      // Arrange
      connectivityService.initialize();

      // Act
      connectivityService.dispose();

      // Assert
      expect(connectivityService.isInitialized, false);
      expect(() => connectivityService.connectivityStream.listen((_) {}), 
          throwsA(isA<StateError>()));
    });
  });

  group('ConnectivityInfo', () {
    test('should provide correct connection type descriptions', () {
      // Test WiFi
      var info = ConnectivityInfo(
        result: ConnectivityResult.wifi,
        isConnected: true,
        hasInternet: true,
        timestamp: DateTime.now(),
      );
      expect(info.connectionType, 'WiFi');
      expect(info.qualityDescription, 'Good (WiFi)');
      expect(info.isSuitableForSync, true);
      expect(info.isMetered, false);

      // Test Mobile
      info = ConnectivityInfo(
        result: ConnectivityResult.mobile,
        isConnected: true,
        hasInternet: true,
        timestamp: DateTime.now(),
      );
      expect(info.connectionType, 'Mobile Data');
      expect(info.qualityDescription, 'Variable (Mobile)');
      expect(info.isMetered, true);

      // Test No Connection
      info = ConnectivityInfo(
        result: ConnectivityResult.none,
        isConnected: false,
        hasInternet: false,
        timestamp: DateTime.now(),
      );
      expect(info.connectionType, 'No Connection');
      expect(info.qualityDescription, 'Disconnected');
      expect(info.isSuitableForSync, false);
    });

    test('should handle connected but no internet scenario', () {
      // Arrange
      final info = ConnectivityInfo(
        result: ConnectivityResult.wifi,
        isConnected: true,
        hasInternet: false,
        timestamp: DateTime.now(),
      );

      // Assert
      expect(info.qualityDescription, 'Connected but no internet');
      expect(info.isSuitableForSync, false);
    });

    test('should implement equality correctly', () {
      // Arrange
      final timestamp = DateTime.now();
      final info1 = ConnectivityInfo(
        result: ConnectivityResult.wifi,
        isConnected: true,
        hasInternet: true,
        timestamp: timestamp,
      );
      final info2 = ConnectivityInfo(
        result: ConnectivityResult.wifi,
        isConnected: true,
        hasInternet: true,
        timestamp: timestamp.add(Duration(seconds: 1)), // Different timestamp
      );
      final info3 = ConnectivityInfo(
        result: ConnectivityResult.mobile,
        isConnected: true,
        hasInternet: true,
        timestamp: timestamp,
      );

      // Assert
      expect(info1, equals(info2)); // Timestamp not considered in equality
      expect(info1, isNot(equals(info3))); // Different result
      expect(info1.hashCode, equals(info2.hashCode));
      expect(info1.hashCode, isNot(equals(info3.hashCode)));
    });
  });

  group('EnhancedConnectivityService', () {
    late EnhancedConnectivityService enhancedService;

    setUp(() {
      enhancedService = EnhancedConnectivityService(
        connectivity: mockConnectivity,
        config: ConnectivityConfig(
          checkInterval: Duration(milliseconds: 100),
          testInternetConnection: false, // Disable for testing
        ),
      );
    });

    tearDown(() {
      enhancedService.dispose();
    });

    test('should initialize with periodic checks', () async {
      // Act
      await enhancedService.initialize();

      // Wait for periodic check
      await Future.delayed(Duration(milliseconds: 150));

      // Assert
      expect(enhancedService.isInitialized, true);
      verify(mockConnectivity.checkConnectivity()).called(greaterThan(1));
    });

    test('should skip internet test when disabled', () async {
      // Act
      final hasInternet = await enhancedService.testInternetConnection();

      // Assert
      expect(hasInternet, true); // Should return true when test is disabled
    });

    test('should handle custom test timeout', () async {
      // Arrange
      final service = EnhancedConnectivityService(
        connectivity: mockConnectivity,
        config: ConnectivityConfig(
          testInternetConnection: true,
          testTimeout: Duration(milliseconds: 1), // Very short timeout
        ),
      );

      // Act
      final hasInternet = await service.testInternetConnection();

      // Assert
      expect(hasInternet, false); // Should timeout and return false

      // Cleanup
      service.dispose();
    });
  });
}
