import 'dart:convert';
import '../../domain/entities/sync_conflict.dart';
import '../datasources/local/sync_local_datasource.dart';

/// Data model for SyncConflict entity
class SyncConflictModel {
  final String id;
  final String entityType;
  final String entityId;
  final String localData;
  final String remoteData;
  final String conflictType;
  final int createdAt;
  final int? resolvedAt;
  final String? resolution;

  const SyncConflictModel({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.localData,
    required this.remoteData,
    required this.conflictType,
    required this.createdAt,
    this.resolvedAt,
    this.resolution,
  });

  /// Convert from domain entity
  factory SyncConflictModel.fromDomain(SyncConflict conflict) {
    return SyncConflictModel(
      id: conflict.id,
      entityType: conflict.entityType.name,
      entityId: conflict.entityId,
      localData: jsonEncode(conflict.localData),
      remoteData: jsonEncode(conflict.remoteData),
      conflictType: conflict.conflictType.name,
      createdAt: conflict.createdAt.millisecondsSinceEpoch,
      resolvedAt: conflict.resolvedAt?.millisecondsSinceEpoch,
      resolution: conflict.resolution?.name,
    );
  }

  /// Convert from database map
  factory SyncConflictModel.fromMap(Map<String, dynamic> map) {
    return SyncConflictModel(
      id: map['id'] as String,
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as String,
      localData: map['local_data'] as String,
      remoteData: map['remote_data'] as String,
      conflictType: map['conflict_type'] as String,
      createdAt: map['created_at'] as int,
      resolvedAt: map['resolved_at'] as int?,
      resolution: map['resolution'] as String?,
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entity_type': entityType,
      'entity_id': entityId,
      'local_data': localData,
      'remote_data': remoteData,
      'conflict_type': conflictType,
      'created_at': createdAt,
      'resolved_at': resolvedAt,
      'resolution': resolution,
    };
  }

  /// Convert to domain entity
  SyncConflict toDomain() {
    return SyncConflict(
      id: id,
      entityType: SyncEntityType.values.byName(entityType),
      entityId: entityId,
      localData: jsonDecode(localData) as Map<String, dynamic>,
      remoteData: jsonDecode(remoteData) as Map<String, dynamic>,
      conflictType: SyncConflictType.values.byName(conflictType),
      createdAt: DateTime.fromMillisecondsSinceEpoch(createdAt),
      resolvedAt: resolvedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(resolvedAt!)
          : null,
      resolution: resolution != null 
          ? SyncConflictResolution.values.byName(resolution!)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entityType': entityType,
      'entityId': entityId,
      'localData': jsonDecode(localData),
      'remoteData': jsonDecode(remoteData),
      'conflictType': conflictType,
      'createdAt': createdAt,
      'resolvedAt': resolvedAt,
      'resolution': resolution,
    };
  }

  /// Convert from JSON
  factory SyncConflictModel.fromJson(Map<String, dynamic> json) {
    return SyncConflictModel(
      id: json['id'] as String,
      entityType: json['entityType'] as String,
      entityId: json['entityId'] as String,
      localData: jsonEncode(json['localData']),
      remoteData: jsonEncode(json['remoteData']),
      conflictType: json['conflictType'] as String,
      createdAt: json['createdAt'] as int,
      resolvedAt: json['resolvedAt'] as int?,
      resolution: json['resolution'] as String?,
    );
  }
}

/// Data model for SyncMetadata
class SyncMetadataModel {
  final String id;
  final int? lastSyncAt;
  final int syncVersion;
  final int conflictCount;

  const SyncMetadataModel({
    required this.id,
    this.lastSyncAt,
    required this.syncVersion,
    required this.conflictCount,
  });

  /// Convert from domain entity
  factory SyncMetadataModel.fromDomain(SyncMetadata metadata) {
    return SyncMetadataModel(
      id: metadata.id,
      lastSyncAt: metadata.lastSyncAt?.millisecondsSinceEpoch,
      syncVersion: metadata.syncVersion,
      conflictCount: metadata.conflictCount,
    );
  }

  /// Convert from database map
  factory SyncMetadataModel.fromMap(Map<String, dynamic> map) {
    return SyncMetadataModel(
      id: map['id'] as String,
      lastSyncAt: map['last_sync_at'] as int?,
      syncVersion: map['sync_version'] as int,
      conflictCount: map['conflict_count'] as int,
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'last_sync_at': lastSyncAt,
      'sync_version': syncVersion,
      'conflict_count': conflictCount,
    };
  }

  /// Convert to domain entity
  SyncMetadata toDomain() {
    return SyncMetadata(
      id: id,
      lastSyncAt: lastSyncAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(lastSyncAt!)
          : null,
      syncVersion: syncVersion,
      conflictCount: conflictCount,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'lastSyncAt': lastSyncAt,
      'syncVersion': syncVersion,
      'conflictCount': conflictCount,
    };
  }

  /// Convert from JSON
  factory SyncMetadataModel.fromJson(Map<String, dynamic> json) {
    return SyncMetadataModel(
      id: json['id'] as String,
      lastSyncAt: json['lastSyncAt'] as int?,
      syncVersion: json['syncVersion'] as int,
      conflictCount: json['conflictCount'] as int,
    );
  }
}

/// Data model for sync operations
class SyncOperationModel {
  final String id;
  final String entityType;
  final String entityId;
  final String operation; // create, update, delete
  final String status; // pending, completed, failed
  final int createdAt;
  final int? completedAt;
  final String? error;

  const SyncOperationModel({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.operation,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.error,
  });

  /// Convert from database map
  factory SyncOperationModel.fromMap(Map<String, dynamic> map) {
    return SyncOperationModel(
      id: map['id'] as String,
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as String,
      operation: map['operation'] as String,
      status: map['status'] as String,
      createdAt: map['created_at'] as int,
      completedAt: map['completed_at'] as int?,
      error: map['error'] as String?,
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entity_type': entityType,
      'entity_id': entityId,
      'operation': operation,
      'status': status,
      'created_at': createdAt,
      'completed_at': completedAt,
      'error': error,
    };
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entityType': entityType,
      'entityId': entityId,
      'operation': operation,
      'status': status,
      'createdAt': createdAt,
      'completedAt': completedAt,
      'error': error,
    };
  }

  /// Convert from JSON
  factory SyncOperationModel.fromJson(Map<String, dynamic> json) {
    return SyncOperationModel(
      id: json['id'] as String,
      entityType: json['entityType'] as String,
      entityId: json['entityId'] as String,
      operation: json['operation'] as String,
      status: json['status'] as String,
      createdAt: json['createdAt'] as int,
      completedAt: json['completedAt'] as int?,
      error: json['error'] as String?,
    );
  }
}
