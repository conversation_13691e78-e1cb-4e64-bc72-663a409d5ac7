// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/unit/providers/hazard_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:geolocator/geolocator.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i11;
import 'package:safestride/domain/entities/hazard.dart' as _i3;
import 'package:safestride/domain/entities/sync_status.dart' as _i9;
import 'package:safestride/domain/entities/walkabout.dart' as _i4;
import 'package:safestride/domain/repositories/hazard_repository.dart' as _i2;
import 'package:safestride/domain/usecases/create_hazard.dart' as _i6;
import 'package:safestride/domain/usecases/update_hazard.dart' as _i8;
import 'package:safestride/services/camera/camera_service.dart' as _i10;
import 'package:safestride/services/location/location_service.dart' as _i12;
import 'package:safestride/services/voice/voice_input_service.dart' as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeHazardRepository_0 extends _i1.SmartFake
    implements _i2.HazardRepository {
  _FakeHazardRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHazard_1 extends _i1.SmartFake implements _i3.Hazard {
  _FakeHazard_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGeoPoint_2 extends _i1.SmartFake implements _i4.GeoPoint {
  _FakeGeoPoint_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLocationSettings_3 extends _i1.SmartFake
    implements _i5.LocationSettings {
  _FakeLocationSettings_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CreateHazardUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreateHazardUseCase extends _i1.Mock
    implements _i6.CreateHazardUseCase {
  MockCreateHazardUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.HazardRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeHazardRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.HazardRepository);

  @override
  _i7.Future<_i3.Hazard> call(_i6.CreateHazardParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i7.Future<_i3.Hazard>.value(
              _FakeHazard_1(this, Invocation.method(#call, [params])),
            ),
          )
          as _i7.Future<_i3.Hazard>);
}

/// A class which mocks [UpdateHazardUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockUpdateHazardUseCase extends _i1.Mock
    implements _i8.UpdateHazardUseCase {
  MockUpdateHazardUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.HazardRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeHazardRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.HazardRepository);

  @override
  _i7.Future<_i3.Hazard> call(_i8.UpdateHazardParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i7.Future<_i3.Hazard>.value(
              _FakeHazard_1(this, Invocation.method(#call, [params])),
            ),
          )
          as _i7.Future<_i3.Hazard>);
}

/// A class which mocks [HazardRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockHazardRepository extends _i1.Mock implements _i2.HazardRepository {
  MockHazardRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i3.Hazard> createHazard(_i3.Hazard? hazard) =>
      (super.noSuchMethod(
            Invocation.method(#createHazard, [hazard]),
            returnValue: _i7.Future<_i3.Hazard>.value(
              _FakeHazard_1(this, Invocation.method(#createHazard, [hazard])),
            ),
          )
          as _i7.Future<_i3.Hazard>);

  @override
  _i7.Future<_i3.Hazard?> getHazardById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardById, [id]),
            returnValue: _i7.Future<_i3.Hazard?>.value(),
          )
          as _i7.Future<_i3.Hazard?>);

  @override
  _i7.Future<List<_i3.Hazard>> getHazardsByWalkaboutId(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByWalkaboutId, [walkaboutId]),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<List<_i3.Hazard>> getHazardsBySeverity(
    String? walkaboutId,
    _i3.HazardSeverity? severity,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsBySeverity, [walkaboutId, severity]),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<List<_i3.Hazard>> getHazardsByCategory(
    String? walkaboutId,
    _i3.HazardCategory? category,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByCategory, [walkaboutId, category]),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<_i3.Hazard> updateHazard(_i3.Hazard? hazard) =>
      (super.noSuchMethod(
            Invocation.method(#updateHazard, [hazard]),
            returnValue: _i7.Future<_i3.Hazard>.value(
              _FakeHazard_1(this, Invocation.method(#updateHazard, [hazard])),
            ),
          )
          as _i7.Future<_i3.Hazard>);

  @override
  _i7.Future<bool> deleteHazard(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteHazard, [id]),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<List<_i3.Hazard>> getHazardsToSync() =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsToSync, []),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<_i3.Hazard> updateSyncStatus(
    String? id,
    _i9.SyncStatus? syncStatus,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateSyncStatus, [id, syncStatus]),
            returnValue: _i7.Future<_i3.Hazard>.value(
              _FakeHazard_1(
                this,
                Invocation.method(#updateSyncStatus, [id, syncStatus]),
              ),
            ),
          )
          as _i7.Future<_i3.Hazard>);

  @override
  _i7.Future<List<_i3.Hazard>> searchHazards(
    String? walkaboutId,
    String? query,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#searchHazards, [walkaboutId, query]),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<List<_i3.Hazard>> getHazardsInArea(
    String? walkaboutId,
    _i4.GeoPoint? center,
    double? radiusMeters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsInArea, [
              walkaboutId,
              center,
              radiusMeters,
            ]),
            returnValue: _i7.Future<List<_i3.Hazard>>.value(<_i3.Hazard>[]),
          )
          as _i7.Future<List<_i3.Hazard>>);

  @override
  _i7.Future<Map<String, dynamic>> getHazardStatistics(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardStatistics, [walkaboutId]),
            returnValue: _i7.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i7.Future<Map<String, dynamic>>);
}

/// A class which mocks [CameraService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCameraService extends _i1.Mock implements _i10.CameraService {
  MockCameraService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<String?> capturePhoto() =>
      (super.noSuchMethod(
            Invocation.method(#capturePhoto, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<String?> selectFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectFromGallery, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<List<String>> selectMultipleFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectMultipleFromGallery, []),
            returnValue: _i7.Future<List<String>>.value(<String>[]),
          )
          as _i7.Future<List<String>>);

  @override
  _i7.Future<String> compressImage(
    String? imagePath, {
    int? targetSizeKB = 200,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #compressImage,
              [imagePath],
              {#targetSizeKB: targetSizeKB},
            ),
            returnValue: _i7.Future<String>.value(
              _i11.dummyValue<String>(
                this,
                Invocation.method(
                  #compressImage,
                  [imagePath],
                  {#targetSizeKB: targetSizeKB},
                ),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  _i7.Future<int> getImageSize(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#getImageSize, [imagePath]),
            returnValue: _i7.Future<int>.value(0),
          )
          as _i7.Future<int>);

  @override
  _i7.Future<bool> deleteImage(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#deleteImage, [imagePath]),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> isCameraAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isCameraAvailable, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> checkCameraPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkCameraPermission, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> requestCameraPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestCameraPermission, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i12.LocationService {
  MockLocationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i4.GeoPoint> getCurrentLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentLocation, []),
            returnValue: _i7.Future<_i4.GeoPoint>.value(
              _FakeGeoPoint_2(this, Invocation.method(#getCurrentLocation, [])),
            ),
          )
          as _i7.Future<_i4.GeoPoint>);

  @override
  _i7.Future<bool> isLocationServiceEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isLocationServiceEnabled, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<_i12.LocationPermissionStatus> checkLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkLocationPermission, []),
            returnValue: _i7.Future<_i12.LocationPermissionStatus>.value(
              _i12.LocationPermissionStatus.denied,
            ),
          )
          as _i7.Future<_i12.LocationPermissionStatus>);

  @override
  _i7.Future<_i12.LocationPermissionStatus> requestLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestLocationPermission, []),
            returnValue: _i7.Future<_i12.LocationPermissionStatus>.value(
              _i12.LocationPermissionStatus.denied,
            ),
          )
          as _i7.Future<_i12.LocationPermissionStatus>);

  @override
  _i7.Future<_i4.GeoPoint> getLocationWithAccuracy(
    _i5.LocationAccuracy? accuracy,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getLocationWithAccuracy, [accuracy]),
            returnValue: _i7.Future<_i4.GeoPoint>.value(
              _FakeGeoPoint_2(
                this,
                Invocation.method(#getLocationWithAccuracy, [accuracy]),
              ),
            ),
          )
          as _i7.Future<_i4.GeoPoint>);

  @override
  double calculateDistance(_i4.GeoPoint? point1, _i4.GeoPoint? point2) =>
      (super.noSuchMethod(
            Invocation.method(#calculateDistance, [point1, point2]),
            returnValue: 0.0,
          )
          as double);

  @override
  bool isWithinRadius(
    _i4.GeoPoint? center,
    _i4.GeoPoint? point,
    double? radiusMeters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#isWithinRadius, [center, point, radiusMeters]),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<_i5.LocationSettings> getLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getLocationSettings, []),
            returnValue: _i7.Future<_i5.LocationSettings>.value(
              _FakeLocationSettings_3(
                this,
                Invocation.method(#getLocationSettings, []),
              ),
            ),
          )
          as _i7.Future<_i5.LocationSettings>);

  @override
  _i7.Future<bool> openLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#openLocationSettings, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);
}

/// A class which mocks [VoiceInputService].
///
/// See the documentation for Mockito's code generation for more information.
class MockVoiceInputService extends _i1.Mock implements _i13.VoiceInputService {
  MockVoiceInputService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isListening =>
      (super.noSuchMethod(Invocation.getter(#isListening), returnValue: false)
          as bool);

  @override
  String get lastRecognizedText =>
      (super.noSuchMethod(
            Invocation.getter(#lastRecognizedText),
            returnValue: _i11.dummyValue<String>(
              this,
              Invocation.getter(#lastRecognizedText),
            ),
          )
          as String);

  @override
  double get lastConfidenceLevel =>
      (super.noSuchMethod(
            Invocation.getter(#lastConfidenceLevel),
            returnValue: 0.0,
          )
          as double);

  @override
  _i7.Future<bool> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> isAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isAvailable, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> checkMicrophonePermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkMicrophonePermission, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> requestMicrophonePermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestMicrophonePermission, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<bool> startListening({
    String? localeId,
    Duration? timeout,
    dynamic Function(String)? onResult,
    dynamic Function(String)? onError,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startListening, [], {
              #localeId: localeId,
              #timeout: timeout,
              #onResult: onResult,
              #onError: onError,
            }),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<void> stopListening() =>
      (super.noSuchMethod(
            Invocation.method(#stopListening, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelListening() =>
      (super.noSuchMethod(
            Invocation.method(#cancelListening, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<List<String>> getAvailableLocales() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableLocales, []),
            returnValue: _i7.Future<List<String>>.value(<String>[]),
          )
          as _i7.Future<List<String>>);
}
