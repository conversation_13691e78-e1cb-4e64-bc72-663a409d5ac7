import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/repositories/walkabout_repository.dart';
import 'package:safestride/domain/usecases/create_walkabout.dart';

import 'create_walkabout_test.mocks.dart';

@GenerateMocks([WalkaboutRepository])
void main() {
  late CreateWalkaboutUseCase useCase;
  late MockWalkaboutRepository mockRepository;

  setUp(() {
    mockRepository = MockWalkaboutRepository();
    useCase = CreateWalkaboutUseCase(repository: mockRepository);
  });

  group('CreateWalkaboutUseCase', () {
    const testUserId = 'test-user-id';
    const testTitle = 'Test Walkabout';
    const testDescription = 'Test description';
    final testLocation = GeoPoint(latitude: 37.7749, longitude: -122.4194);

    test('should create walkabout successfully with all parameters', () async {
      // Arrange
      final params = CreateWalkaboutParams(
        title: testTitle,
        description: testDescription,
        userId: testUserId,
        location: testLocation,
        status: WalkaboutStatus.draft,
      );

      final expectedWalkabout = Walkabout(
        id: 'generated-id',
        title: testTitle,
        description: testDescription,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: WalkaboutStatus.draft,
        location: testLocation,
        userId: testUserId,
        isCompleted: false,
        syncStatus: SyncStatus.local,
        deletedAt: null,
      );

      when(
        mockRepository.createWalkabout(any),
      ).thenAnswer((_) async => expectedWalkabout);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.title, testTitle);
      expect(result.description, testDescription);
      expect(result.userId, testUserId);
      expect(result.location, testLocation);
      expect(result.status, WalkaboutStatus.draft);
      expect(result.isCompleted, false);
      expect(result.syncStatus, SyncStatus.local);

      verify(mockRepository.createWalkabout(any)).called(1);
    });

    test('should create walkabout with minimal parameters', () async {
      // Arrange
      final params = CreateWalkaboutParams(
        title: testTitle,
        userId: testUserId,
      );

      final expectedWalkabout = Walkabout(
        id: 'generated-id',
        title: testTitle,
        description: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: WalkaboutStatus.draft,
        location: null,
        userId: testUserId,
        isCompleted: false,
        syncStatus: SyncStatus.local,
      );

      when(
        mockRepository.createWalkabout(any),
      ).thenAnswer((_) async => expectedWalkabout);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.title, testTitle);
      expect(result.description, null);
      expect(result.location, null);
      expect(result.status, WalkaboutStatus.draft);

      verify(mockRepository.createWalkabout(any)).called(1);
    });

    test('should throw ArgumentError when title is empty', () async {
      // Arrange
      final params = CreateWalkaboutParams(title: '', userId: testUserId);

      // Act & Assert
      expect(
        () => useCase.call(params),
        throwsA(
          isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Walkabout title cannot be empty',
          ),
        ),
      );

      verifyNever(mockRepository.createWalkabout(any));
    });

    test(
      'should throw ArgumentError when title exceeds 100 characters',
      () async {
        // Arrange
        final longTitle = 'a' * 101;
        final params = CreateWalkaboutParams(
          title: longTitle,
          userId: testUserId,
        );

        // Act & Assert
        expect(
          () => useCase.call(params),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              'Walkabout title cannot exceed 100 characters',
            ),
          ),
        );

        verifyNever(mockRepository.createWalkabout(any));
      },
    );

    test('should throw ArgumentError when userId is empty', () async {
      // Arrange
      final params = CreateWalkaboutParams(title: testTitle, userId: '');

      // Act & Assert
      expect(
        () => useCase.call(params),
        throwsA(
          isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'User ID is required',
          ),
        ),
      );

      verifyNever(mockRepository.createWalkabout(any));
    });

    test(
      'should throw ArgumentError when description exceeds 500 characters',
      () async {
        // Arrange
        final longDescription = 'a' * 501;
        final params = CreateWalkaboutParams(
          title: testTitle,
          description: longDescription,
          userId: testUserId,
        );

        // Act & Assert
        expect(
          () => useCase.call(params),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              'Walkabout description cannot exceed 500 characters',
            ),
          ),
        );

        verifyNever(mockRepository.createWalkabout(any));
      },
    );

    test('should throw ArgumentError when latitude is invalid', () async {
      // Arrange
      final invalidLocation = GeoPoint(latitude: 91.0, longitude: 0.0);
      final params = CreateWalkaboutParams(
        title: testTitle,
        userId: testUserId,
        location: invalidLocation,
      );

      // Act & Assert
      expect(
        () => useCase.call(params),
        throwsA(
          isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Invalid latitude: must be between -90 and 90',
          ),
        ),
      );

      verifyNever(mockRepository.createWalkabout(any));
    });

    test(
      'should throw ArgumentError when title already exists for user',
      () async {
        // Arrange
        final params = CreateWalkaboutParams(
          title: testTitle,
          userId: testUserId,
        );

        when(
          mockRepository.titleExistsForUser(testUserId, testTitle),
        ).thenAnswer((_) async => true);

        // Act & Assert
        expect(
          () => useCase.call(params),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              'A walkabout with this title already exists',
            ),
          ),
        );

        verify(
          mockRepository.titleExistsForUser(testUserId, testTitle),
        ).called(1);
        verifyNever(mockRepository.createWalkabout(any));
      },
    );

    test('should create walkabout when title is unique for user', () async {
      // Arrange
      final params = CreateWalkaboutParams(
        title: testTitle,
        userId: testUserId,
      );

      final expectedWalkabout = Walkabout(
        id: 'generated-id',
        title: testTitle,
        description: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: WalkaboutStatus.draft,
        location: null,
        userId: testUserId,
        isCompleted: false,
        syncStatus: SyncStatus.local,
        deletedAt: null,
      );

      when(
        mockRepository.titleExistsForUser(testUserId, testTitle),
      ).thenAnswer((_) async => false);
      when(
        mockRepository.createWalkabout(any),
      ).thenAnswer((_) async => expectedWalkabout);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.title, equals(testTitle));
      expect(result.userId, equals(testUserId));
      verify(
        mockRepository.titleExistsForUser(testUserId, testTitle),
      ).called(1);
      verify(mockRepository.createWalkabout(any)).called(1);
    });

    test('should throw ArgumentError when longitude is invalid', () async {
      // Arrange
      final invalidLocation = GeoPoint(latitude: 0.0, longitude: 181.0);
      final params = CreateWalkaboutParams(
        title: testTitle,
        userId: testUserId,
        location: invalidLocation,
      );

      // Act & Assert
      expect(
        () => useCase.call(params),
        throwsA(
          isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Invalid longitude: must be between -180 and 180',
          ),
        ),
      );

      verifyNever(mockRepository.createWalkabout(any));
    });

    test('should propagate repository exceptions', () async {
      // Arrange
      final params = CreateWalkaboutParams(
        title: testTitle,
        userId: testUserId,
      );

      when(
        mockRepository.createWalkabout(any),
      ).thenThrow(Exception('Database error'));

      // Act & Assert
      expect(
        () => useCase.call(params),
        throwsA(
          isA<Exception>().having(
            (e) => e.toString(),
            'message',
            'Exception: Database error',
          ),
        ),
      );
    });
  });
}
