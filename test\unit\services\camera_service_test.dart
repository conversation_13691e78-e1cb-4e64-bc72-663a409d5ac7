import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safestride/services/camera/camera_service.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;

import 'camera_service_test.mocks.dart';

@GenerateMocks([ImagePicker])
void main() {
  group('CameraService Tests', () {
    late CameraServiceImpl cameraService;
    late MockImagePicker mockImagePicker;
    late Directory tempDir;

    setUpAll(() async {
      // Create temporary directory for test files
      tempDir = await Directory.systemTemp.createTemp('camera_service_test');
    });

    tearDownAll(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    setUp(() {
      mockImagePicker = MockImagePicker();
      cameraService = CameraServiceImpl();
    });

    group('Photo Compression Tests', () {
      test('should compress large image to meet 200KB target', () async {
        // Arrange - Create a large test image
        final testImage = img.Image(width: 3000, height: 2000);
        img.fill(testImage, color: img.ColorRgb8(255, 0, 0)); // Red image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 100);
        final testImagePath = path.join(tempDir.path, 'large_test_image.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Verify original image is larger than 200KB
        final originalSize = await testImageFile.length();
        expect(originalSize, greaterThan(200 * 1024));

        // Act
        final compressedPath = await cameraService.compressImage(testImagePath);

        // Assert
        final compressedFile = File(compressedPath);
        expect(await compressedFile.exists(), isTrue);
        
        final compressedSize = await compressedFile.length();
        expect(compressedSize, lessThanOrEqualTo(200 * 1024)); // 200KB target
        expect(compressedSize, lessThan(originalSize)); // Should be smaller than original

        // Clean up
        await testImageFile.delete();
        await compressedFile.delete();
      });

      test('should return original path if image is already under 200KB', () async {
        // Arrange - Create a small test image
        final testImage = img.Image(width: 100, height: 100);
        img.fill(testImage, color: img.ColorRgb8(0, 255, 0)); // Green image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 50);
        final testImagePath = path.join(tempDir.path, 'small_test_image.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Verify original image is smaller than 200KB
        final originalSize = await testImageFile.length();
        expect(originalSize, lessThan(200 * 1024));

        // Act
        final compressedPath = await cameraService.compressImage(testImagePath);

        // Assert
        expect(compressedPath, equals(testImagePath)); // Should return original path

        // Clean up
        await testImageFile.delete();
      });

      test('should compress image with custom target size', () async {
        // Arrange - Create a test image
        final testImage = img.Image(width: 2000, height: 1500);
        img.fill(testImage, color: img.ColorRgb8(0, 0, 255)); // Blue image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 100);
        final testImagePath = path.join(tempDir.path, 'custom_target_test.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        const customTargetKB = 100; // 100KB target

        // Act
        final compressedPath = await cameraService.compressImage(
          testImagePath,
          targetSizeKB: customTargetKB,
        );

        // Assert
        final compressedFile = File(compressedPath);
        expect(await compressedFile.exists(), isTrue);
        
        final compressedSize = await compressedFile.length();
        expect(compressedSize, lessThanOrEqualTo(customTargetKB * 1024));

        // Clean up
        await testImageFile.delete();
        await compressedFile.delete();
      });

      test('should handle compression of very large images', () async {
        // Arrange - Create a very large test image
        final testImage = img.Image(width: 4000, height: 3000);
        img.fill(testImage, color: img.ColorRgb8(128, 128, 128)); // Gray image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 100);
        final testImagePath = path.join(tempDir.path, 'very_large_test.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Verify original image is very large
        final originalSize = await testImageFile.length();
        expect(originalSize, greaterThan(1024 * 1024)); // > 1MB

        // Act
        final compressedPath = await cameraService.compressImage(testImagePath);

        // Assert
        final compressedFile = File(compressedPath);
        expect(await compressedFile.exists(), isTrue);
        
        final compressedSize = await compressedFile.length();
        expect(compressedSize, lessThanOrEqualTo(200 * 1024)); // 200KB target
        
        // Verify image is still readable after compression
        final compressedBytes = await compressedFile.readAsBytes();
        final decodedImage = img.decodeImage(compressedBytes);
        expect(decodedImage, isNotNull);
        expect(decodedImage!.width, lessThanOrEqualTo(1920)); // Max width constraint
        expect(decodedImage.height, lessThanOrEqualTo(1080)); // Max height constraint

        // Clean up
        await testImageFile.delete();
        await compressedFile.delete();
      });

      test('should throw exception for non-existent image file', () async {
        // Arrange
        const nonExistentPath = '/non/existent/path/image.jpg';

        // Act & Assert
        expect(
          () => cameraService.compressImage(nonExistentPath),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Image file not found'),
          )),
        );
      });

      test('should throw exception for invalid image file', () async {
        // Arrange - Create a non-image file
        final invalidImagePath = path.join(tempDir.path, 'invalid_image.jpg');
        final invalidImageFile = File(invalidImagePath);
        await invalidImageFile.writeAsString('This is not an image');

        // Act & Assert
        expect(
          () => cameraService.compressImage(invalidImagePath),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to decode image'),
          )),
        );

        // Clean up
        await invalidImageFile.delete();
      });

      test('should maintain aspect ratio during compression', () async {
        // Arrange - Create a rectangular test image
        final testImage = img.Image(width: 3000, height: 1500); // 2:1 aspect ratio
        img.fill(testImage, color: img.ColorRgb8(255, 255, 0)); // Yellow image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 100);
        final testImagePath = path.join(tempDir.path, 'aspect_ratio_test.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Act
        final compressedPath = await cameraService.compressImage(testImagePath);

        // Assert
        final compressedFile = File(compressedPath);
        final compressedBytes = await compressedFile.readAsBytes();
        final decodedImage = img.decodeImage(compressedBytes);
        
        expect(decodedImage, isNotNull);
        
        // Check aspect ratio is maintained (allowing for small rounding differences)
        final originalAspectRatio = 3000 / 1500; // 2.0
        final compressedAspectRatio = decodedImage!.width / decodedImage.height;
        expect(compressedAspectRatio, closeTo(originalAspectRatio, 0.1));

        // Clean up
        await testImageFile.delete();
        await compressedFile.delete();
      });
    });

    group('Image Size Tests', () {
      test('should return correct image file size', () async {
        // Arrange - Create a test image
        final testImage = img.Image(width: 500, height: 500);
        img.fill(testImage, color: img.ColorRgb8(255, 0, 255)); // Magenta image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 80);
        final testImagePath = path.join(tempDir.path, 'size_test_image.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Act
        final size = await cameraService.getImageSize(testImagePath);

        // Assert
        expect(size, equals(testImageBytes.length));
        expect(size, greaterThan(0));

        // Clean up
        await testImageFile.delete();
      });

      test('should throw exception for non-existent file when getting size', () async {
        // Arrange
        const nonExistentPath = '/non/existent/path/image.jpg';

        // Act & Assert
        expect(
          () => cameraService.getImageSize(nonExistentPath),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Image file not found'),
          )),
        );
      });
    });

    group('Image Deletion Tests', () {
      test('should delete existing image file', () async {
        // Arrange - Create a test image
        final testImage = img.Image(width: 200, height: 200);
        img.fill(testImage, color: img.ColorRgb8(0, 255, 255)); // Cyan image
        
        final testImageBytes = img.encodeJpg(testImage, quality: 70);
        final testImagePath = path.join(tempDir.path, 'delete_test_image.jpg');
        final testImageFile = File(testImagePath);
        await testImageFile.writeAsBytes(testImageBytes);

        // Verify file exists
        expect(await testImageFile.exists(), isTrue);

        // Act
        final deleted = await cameraService.deleteImage(testImagePath);

        // Assert
        expect(deleted, isTrue);
        expect(await testImageFile.exists(), isFalse);
      });

      test('should return false when deleting non-existent file', () async {
        // Arrange
        const nonExistentPath = '/non/existent/path/image.jpg';

        // Act
        final deleted = await cameraService.deleteImage(nonExistentPath);

        // Assert
        expect(deleted, isFalse);
      });
    });
  });
}
