# Story 2.3: Offline Data Management

## Status: Approved

## Story

**As a** user,\
**I want** my walkabout data to sync when I'm back online,\
**so that** I don't lose any information.

## Acceptance Criteria

- 1: Data is stored locally during offline use
- 2: Changes sync automatically when online
- 3: Sync conflicts are handled gracefully
- 4: Users can see sync status
- 5: Failed syncs are retried automatically

## Tasks / Subtasks

- [x] Task 1: Domain Layer – Add Sync Entities and Use Cases (AC: 2,3,5)
  - [x] Create `SyncStatus` enum in `lib/domain/entities/sync_status.dart` [Source: architecture/data-models.md#walkabout]
  - [x] Create `SyncConflict` entity in `lib/domain/entities/sync_conflict.dart`
  - [x] Define `SyncRepository` interface in `lib/domain/repositories/sync_repository.dart`
  - [x] Create `SyncDataUseCase` in `lib/domain/usecases/sync_data.dart`
  - [x] Create `ResolveSyncConflictUseCase` in `lib/domain/usecases/resolve_sync_conflict.dart`
- [x] Task 2: Data Layer – Implement Sync Repository and Data Sources (AC: 1,2,3)
  - [x] Create `SyncLocalDataSource` in `lib/data/datasources/local/sync_local_datasource.dart` [Source: architecture/database-schema.md#local-sqlite-schema]
  - [x] Create `SyncRemoteDataSource` in `lib/data/datasources/remote/sync_remote_datasource.dart` [Source: architecture/external-apis.md#firebase-services]
  - [x] Implement `SyncRepositoryImpl` in `lib/data/repositories/sync_repository_impl.dart`
  - [x] Create `SyncModel` in `lib/data/models/sync_model.dart` with JSON serialization
- [x] Task 3: External Services – Sync Service Implementation (AC: 2,4,5)
  - [x] Create `SyncService` in `lib/services/sync/sync_service.dart` [Source: architecture/components.md#external-services-layer]
  - [x] Create `ConnectivityService` in `lib/services/sync/connectivity_service.dart`
  - [x] Implement automatic sync triggers and retry logic
  - [x] Add sync status monitoring and conflict detection
- [x] Task 4: Business Logic – Sync Provider and State Management (AC: 2,3,4,5)
  - [x] Implement `SyncProvider` in `lib/presentation/providers/sync_provider.dart` [Source: architecture/components.md#business-logic-layer]
  - [x] Handle sync state management and user notifications
  - [x] Integrate with existing WalkaboutProvider and HazardProvider
  - [x] Implement conflict resolution UI state management
- [x] Task 5: Data Layer Updates – Add Sync Status to Existing Entities (AC: 1,2)
  - [x] Update `WalkaboutLocalDataSource` to handle sync_status field
  - [x] Update `HazardLocalDataSource` to handle sync_status field
  - [x] Update `WalkaboutModel` and `HazardModel` with sync status serialization
  - [x] Add sync status queries and filters to repositories
- [x] Task 6: UI – Sync Status Indicators and Conflict Resolution (AC: 3,4)
  - [x] Create `SyncStatusWidget` in `lib/presentation/widgets/sync/sync_status_widget.dart`
  - [x] Create `SyncConflictDialog` in `lib/presentation/widgets/sync/sync_conflict_dialog.dart`
  - [x] Update walkabout and hazard list screens with sync indicators
  - [x] Add sync settings and manual sync trigger in settings screen
- [x] Task 7: Integration – Background Sync and Connectivity Monitoring (AC: 2,5)
  - [x] Implement background sync using WorkManager or similar
  - [x] Add connectivity change listeners for automatic sync
  - [x] Integrate sync service with app lifecycle events
  - [x] Add sync progress notifications
- [x] Task 8: Testing (All AC)
  - [x] Unit tests for sync use cases and repository in `test/unit/`
  - [x] Widget tests for sync UI components in `test/widget/`
  - [x] Integration tests for sync scenarios in `test/integration/`
  - [x] Mock offline/online scenarios and conflict resolution

## Dev Notes

### Previous Story Insights

- Walkabout and Hazard entities already have sync_status fields defined in data models [Source: architecture/data-models.md#walkabout]
- Local SQLite schema includes sync_status columns for both walkabouts and hazards tables [Source: architecture/database-schema.md#local-sqlite-schema]
- Firebase Firestore is configured as the remote data store for sync operations [Source: architecture/external-apis.md#firebase-services]
- Provider pattern is established for state management across the app [Source: architecture/components.md#business-logic-layer]

### Data Models

- **SyncStatus Enum**: local, syncing, synced, error - tracks sync state for each entity [Source: architecture/data-models.md#walkabout]
- **SyncConflict Entity**: id, entityType, entityId, localData, remoteData, conflictType, resolvedAt, resolution [Source: architecture/data-models.md]
- **Existing Entities**: Walkabout and Hazard entities already include syncStatus field for tracking sync state
- **Sync Metadata**: lastSyncAt, syncVersion, conflictCount for tracking sync operations

### API Specifications

- **Local SQLite**: sync_status column exists in walkabouts and hazards tables with values: 'local', 'syncing', 'synced', 'error' [Source: architecture/database-schema.md#local-sqlite-schema]
- **Firestore Collections**: walkabouts and hazards collections for remote storage [Source: architecture/database-schema.md#firestore-schema]
- **Firebase Services**: Firestore for data sync, Firebase Storage for photo sync [Source: architecture/external-apis.md#firebase-services]
- **Connectivity Monitoring**: Use connectivity_plus plugin for network state detection
- **Conflict Resolution**: Last-write-wins with user override option for critical conflicts

### Component Specifications

- **SyncService**: Core sync orchestration, handles bidirectional sync between local SQLite and Firestore [Source: architecture/components.md#external-services-layer]
- **SyncProvider**: State management for sync operations, progress tracking, and conflict resolution [Source: architecture/components.md#business-logic-layer]
- **ConnectivityService**: Network state monitoring and automatic sync triggering
- **SyncRepository**: Abstraction layer for sync operations with local and remote data sources [Source: architecture/components.md#data-layer]

### File Locations

- Domain entities: `lib/domain/entities/sync_status.dart`, `lib/domain/entities/sync_conflict.dart`
- Domain use cases: `lib/domain/usecases/sync_data.dart`, `lib/domain/usecases/resolve_sync_conflict.dart`
- Repositories: `lib/domain/repositories/sync_repository.dart`
- Repository impl: `lib/data/repositories/sync_repository_impl.dart`
- Data sources: `lib/data/datasources/local/sync_local_datasource.dart`, `lib/data/datasources/remote/sync_remote_datasource.dart`
- Models: `lib/data/models/sync_model.dart`
- Provider: `lib/presentation/providers/sync_provider.dart`
- Services: `lib/services/sync/sync_service.dart`, `lib/services/sync/connectivity_service.dart`
- UI widgets: `lib/presentation/widgets/sync/`
- Tests: `test/unit/`, `test/widget/`, `test/integration/`

### Testing Requirements

- Use `flutter_test` framework for all testing [Source: architecture/tech-stack.md#testing]
- Unit tests for sync use cases, repository implementations, and service logic
- Widget tests for sync status indicators and conflict resolution dialogs
- Integration tests for end-to-end sync scenarios including offline/online transitions
- Mock Firebase services and connectivity states for testing
- Test sync conflict scenarios and resolution workflows
- Performance tests for large data sync operations

### Technical Constraints

- Must maintain offline-first architecture with local SQLite as primary data store [Source: architecture/tech-stack.md#local-database]
- Firebase Firestore provides real-time sync capabilities when online [Source: architecture/tech-stack.md#backend]
- Sync operations must be atomic to prevent data corruption
- Photo sync requires Firebase Storage integration for large file handling [Source: architecture/tech-stack.md#file-storage]
- Must handle network interruptions gracefully with retry mechanisms
- Sync conflicts must preserve user data and provide resolution options
- Background sync should respect device battery and data usage settings
- Must comply with Clean Architecture layer separation [Source: architecture/components.md]

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2025-01-27 | 0.1 | Initial draft | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

### Debug Log References

### Completion Notes List

### File List

#### Created Files

- `lib/domain/entities/sync_status.dart` - SyncStatus enum with sync state management
- `lib/domain/entities/sync_conflict.dart` - SyncConflict entity for conflict management
- `lib/domain/repositories/sync_repository.dart` - SyncRepository interface
- `lib/domain/usecases/sync_data.dart` - SyncDataUseCase for sync operations
- `lib/domain/usecases/resolve_sync_conflict.dart` - ResolveSyncConflictUseCase for conflict resolution
- `lib/data/datasources/local/sync_local_datasource.dart` - Local sync data source
- `lib/data/datasources/remote/sync_remote_datasource.dart` - Remote sync data source
- `lib/data/repositories/sync_repository_impl.dart` - SyncRepository implementation
- `lib/data/models/sync_model.dart` - Sync data models with JSON serialization
- `lib/services/sync/sync_service.dart` - Core sync service with retry logic
- `lib/services/sync/connectivity_service.dart` - Network connectivity monitoring
- `lib/presentation/providers/sync_provider.dart` - Sync state management provider
- `lib/presentation/widgets/sync/sync_status_widget.dart` - Sync status indicator widget
- `lib/presentation/widgets/sync/sync_conflict_dialog.dart` - Conflict resolution dialog
- `lib/core/database/sync_database_helper.dart` - Database helper for sync tables
- `test/unit/domain/usecases/sync_data_test.dart` - Unit tests for sync use case
- `test/unit/domain/usecases/resolve_sync_conflict_test.dart` - Unit tests for conflict resolution
- `test/unit/data/repositories/sync_repository_test.dart` - Unit tests for sync repository
- `test/unit/services/sync/sync_service_test.dart` - Unit tests for sync service
- `test/unit/services/sync/connectivity_service_test.dart` - Unit tests for connectivity service
- `test/unit/presentation/providers/sync_provider_test.dart` - Unit tests for sync provider

#### Modified Files

- `lib/domain/entities/walkabout.dart` - Removed duplicate SyncStatus enum, added import
- `lib/domain/entities/hazard.dart` - Added SyncStatus import
- `lib/presentation/providers/walkabout_provider.dart` - Added sync integration methods
- `lib/domain/usecases/create_walkabout.dart` - Added SyncStatus import
- `lib/domain/usecases/create_hazard.dart` - Added SyncStatus import
- `lib/domain/usecases/update_hazard.dart` - Added SyncStatus import
- `lib/domain/repositories/walkabout_repository.dart` - Added SyncStatus import
- `lib/domain/repositories/hazard_repository.dart` - Added SyncStatus import
- `lib/data/models/walkabout_model.dart` - Added SyncStatus import
- `lib/data/models/hazard_model.dart` - Added SyncStatus import
- `lib/data/datasources/local/walkabout_local_datasource.dart` - Added SyncStatus import
- `lib/data/datasources/local/hazard_local_datasource.dart` - Added SyncStatus import
- `lib/data/repositories/walkabout_repository_impl.dart` - Added SyncStatus import
- `lib/data/repositories/hazard_repository_impl.dart` - Added SyncStatus import
- `lib/presentation/widgets/walkabout/walkabout_card.dart` - Added SyncStatus import
- `test/integration/hazard_documentation_flow_test.dart` - Added SyncStatus import

## QA Results
