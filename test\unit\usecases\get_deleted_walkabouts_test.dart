import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/usecases/get_deleted_walkabouts.dart';
import 'package:safestride/domain/repositories/walkabout_repository.dart';

import 'create_walkabout_test.mocks.dart';

@GenerateMocks([WalkaboutRepository])
void main() {
  late GetDeletedWalkaboutsUseCase useCase;
  late MockWalkaboutRepository mockRepository;

  setUp(() {
    mockRepository = MockWalkaboutRepository();
    useCase = GetDeletedWalkaboutsUseCase(repository: mockRepository);
  });

  group('GetDeletedWalkaboutsUseCase', () {
    const testUserId = 'test-user-id';
    final deletedWalkabouts = [
      Walkabout(
        id: 'deleted-walkabout-1',
        title: 'Deleted Walkabout 1',
        description: 'First deleted walkabout',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        status: WalkaboutStatus.draft,
        location: GeoPoint(latitude: 37.7749, longitude: -122.4194),
        userId: testUserId,
        isCompleted: false,
        syncStatus: SyncStatus.local,
        deletedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      Walkabout(
        id: 'deleted-walkabout-2',
        title: 'Deleted Walkabout 2',
        description: 'Second deleted walkabout',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        status: WalkaboutStatus.completed,
        location: null,
        userId: testUserId,
        isCompleted: true,
        syncStatus: SyncStatus.synced,
        deletedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];

    test('should return deleted walkabouts for user', () async {
      // Arrange
      when(mockRepository.getDeletedWalkaboutsByUserId(testUserId))
          .thenAnswer((_) async => deletedWalkabouts);

      // Act
      final result = await useCase.call(testUserId);

      // Assert
      expect(result, equals(deletedWalkabouts));
      expect(result.length, equals(2));
      expect(result.every((w) => w.deletedAt != null), isTrue);
      expect(result.every((w) => w.userId == testUserId), isTrue);
      verify(mockRepository.getDeletedWalkaboutsByUserId(testUserId)).called(1);
    });

    test('should return empty list when no deleted walkabouts found', () async {
      // Arrange
      when(mockRepository.getDeletedWalkaboutsByUserId(testUserId))
          .thenAnswer((_) async => []);

      // Act
      final result = await useCase.call(testUserId);

      // Assert
      expect(result, isEmpty);
      verify(mockRepository.getDeletedWalkaboutsByUserId(testUserId)).called(1);
    });

    test('should throw ArgumentError when user ID is empty', () async {
      // Act & Assert
      expect(
        () => useCase.call(''),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'User ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.getDeletedWalkaboutsByUserId(any));
    });

    test('should throw ArgumentError when user ID is whitespace', () async {
      // Act & Assert
      expect(
        () => useCase.call('   '),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'User ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.getDeletedWalkaboutsByUserId(any));
    });

    test('should propagate repository exceptions', () async {
      // Arrange
      when(mockRepository.getDeletedWalkaboutsByUserId(testUserId))
          .thenThrow(Exception('Database error'));

      // Act & Assert
      expect(
        () => useCase.call(testUserId),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Database error'),
        )),
      );
      
      verify(mockRepository.getDeletedWalkaboutsByUserId(testUserId)).called(1);
    });
  });
}
