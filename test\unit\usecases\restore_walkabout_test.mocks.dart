// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in safestride/test/unit/usecases/restore_walkabout_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/sync_status.dart' as _i5;
import 'package:safestride/domain/entities/walkabout.dart' as _i2;
import 'package:safestride/domain/repositories/walkabout_repository.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeWalkabout_0 extends _i1.SmartFake implements _i2.Walkabout {
  _FakeWalkabout_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [WalkaboutRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalkaboutRepository extends _i1.Mock
    implements _i3.WalkaboutRepository {
  MockWalkaboutRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Walkabout> createWalkabout(_i2.Walkabout? walkabout) =>
      (super.noSuchMethod(
            Invocation.method(#createWalkabout, [walkabout]),
            returnValue: _i4.Future<_i2.Walkabout>.value(
              _FakeWalkabout_0(
                this,
                Invocation.method(#createWalkabout, [walkabout]),
              ),
            ),
          )
          as _i4.Future<_i2.Walkabout>);

  @override
  _i4.Future<_i2.Walkabout?> getWalkaboutById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutById, [id]),
            returnValue: _i4.Future<_i2.Walkabout?>.value(),
          )
          as _i4.Future<_i2.Walkabout?>);

  @override
  _i4.Future<List<_i2.Walkabout>> getWalkaboutsByUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutsByUserId, [userId]),
            returnValue: _i4.Future<List<_i2.Walkabout>>.value(
              <_i2.Walkabout>[],
            ),
          )
          as _i4.Future<List<_i2.Walkabout>>);

  @override
  _i4.Future<List<_i2.Walkabout>> getWalkaboutsByStatus(
    String? userId,
    _i2.WalkaboutStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutsByStatus, [userId, status]),
            returnValue: _i4.Future<List<_i2.Walkabout>>.value(
              <_i2.Walkabout>[],
            ),
          )
          as _i4.Future<List<_i2.Walkabout>>);

  @override
  _i4.Future<_i2.Walkabout> updateWalkabout(_i2.Walkabout? walkabout) =>
      (super.noSuchMethod(
            Invocation.method(#updateWalkabout, [walkabout]),
            returnValue: _i4.Future<_i2.Walkabout>.value(
              _FakeWalkabout_0(
                this,
                Invocation.method(#updateWalkabout, [walkabout]),
              ),
            ),
          )
          as _i4.Future<_i2.Walkabout>);

  @override
  _i4.Future<bool> deleteWalkabout(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteWalkabout, [id]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<List<_i2.Walkabout>> getWalkaboutsToSync() =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutsToSync, []),
            returnValue: _i4.Future<List<_i2.Walkabout>>.value(
              <_i2.Walkabout>[],
            ),
          )
          as _i4.Future<List<_i2.Walkabout>>);

  @override
  _i4.Future<_i2.Walkabout> updateSyncStatus(
    String? id,
    _i5.SyncStatus? syncStatus,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateSyncStatus, [id, syncStatus]),
            returnValue: _i4.Future<_i2.Walkabout>.value(
              _FakeWalkabout_0(
                this,
                Invocation.method(#updateSyncStatus, [id, syncStatus]),
              ),
            ),
          )
          as _i4.Future<_i2.Walkabout>);

  @override
  _i4.Future<bool> titleExistsForUser(String? userId, String? title) =>
      (super.noSuchMethod(
            Invocation.method(#titleExistsForUser, [userId, title]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.Walkabout> restoreWalkabout(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#restoreWalkabout, [id]),
            returnValue: _i4.Future<_i2.Walkabout>.value(
              _FakeWalkabout_0(
                this,
                Invocation.method(#restoreWalkabout, [id]),
              ),
            ),
          )
          as _i4.Future<_i2.Walkabout>);

  @override
  _i4.Future<List<_i2.Walkabout>> getDeletedWalkaboutsByUserId(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getDeletedWalkaboutsByUserId, [userId]),
            returnValue: _i4.Future<List<_i2.Walkabout>>.value(
              <_i2.Walkabout>[],
            ),
          )
          as _i4.Future<List<_i2.Walkabout>>);

  @override
  _i4.Future<bool> permanentlyDeleteWalkabout(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#permanentlyDeleteWalkabout, [id]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);
}
