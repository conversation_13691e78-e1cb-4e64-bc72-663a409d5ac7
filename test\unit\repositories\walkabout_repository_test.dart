import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/data/repositories/walkabout_repository_impl.dart';
import 'package:safestride/data/datasources/local/walkabout_local_datasource.dart';

import 'walkabout_repository_test.mocks.dart';

@GenerateMocks([WalkaboutLocalDataSource])
void main() {
  late WalkaboutRepositoryImpl repository;
  late MockWalkaboutLocalDataSource mockLocalDataSource;

  setUp(() {
    mockLocalDataSource = MockWalkaboutLocalDataSource();
    repository = WalkaboutRepositoryImpl(localDataSource: mockLocalDataSource);
  });

  group('WalkaboutRepositoryImpl', () {
    final testWalkabout = Walkabout(
      id: 'test-id',
      title: 'Test Walkabout',
      description: 'Test description',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      status: WalkaboutStatus.draft,
      location: GeoPoint(latitude: 37.7749, longitude: -122.4194),
      userId: 'test-user-id',
      isCompleted: false,
      syncStatus: SyncStatus.local,
      deletedAt: null,
    );

    group('createWalkabout', () {
      test('should create walkabout successfully', () async {
        // Arrange
        when(
          mockLocalDataSource.createWalkabout(any),
        ).thenAnswer((_) async => testWalkabout);

        // Act
        final result = await repository.createWalkabout(testWalkabout);

        // Assert
        expect(result, equals(testWalkabout));
        verify(mockLocalDataSource.createWalkabout(testWalkabout)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.createWalkabout(any),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.createWalkabout(testWalkabout),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to create walkabout'),
            ),
          ),
        );
      });
    });

    group('getWalkaboutById', () {
      test('should return walkabout when found', () async {
        // Arrange
        when(
          mockLocalDataSource.getWalkaboutById('test-id'),
        ).thenAnswer((_) async => testWalkabout);

        // Act
        final result = await repository.getWalkaboutById('test-id');

        // Assert
        expect(result, equals(testWalkabout));
        verify(mockLocalDataSource.getWalkaboutById('test-id')).called(1);
      });

      test('should return null when not found', () async {
        // Arrange
        when(
          mockLocalDataSource.getWalkaboutById('non-existent'),
        ).thenAnswer((_) async => null);

        // Act
        final result = await repository.getWalkaboutById('non-existent');

        // Assert
        expect(result, isNull);
        verify(mockLocalDataSource.getWalkaboutById('non-existent')).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getWalkaboutById(any),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getWalkaboutById('test-id'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get walkabout by ID'),
            ),
          ),
        );
      });
    });

    group('getWalkaboutsByUserId', () {
      test('should return list of walkabouts', () async {
        // Arrange
        final walkabouts = [testWalkabout];
        when(
          mockLocalDataSource.getWalkaboutsByUserId('test-user-id'),
        ).thenAnswer((_) async => walkabouts);

        // Act
        final result = await repository.getWalkaboutsByUserId('test-user-id');

        // Assert
        expect(result, equals(walkabouts));
        verify(
          mockLocalDataSource.getWalkaboutsByUserId('test-user-id'),
        ).called(1);
      });

      test('should return empty list when no walkabouts found', () async {
        // Arrange
        when(
          mockLocalDataSource.getWalkaboutsByUserId('test-user-id'),
        ).thenAnswer((_) async => []);

        // Act
        final result = await repository.getWalkaboutsByUserId('test-user-id');

        // Assert
        expect(result, isEmpty);
        verify(
          mockLocalDataSource.getWalkaboutsByUserId('test-user-id'),
        ).called(1);
      });
    });

    group('updateWalkabout', () {
      test('should update walkabout and set sync status to local', () async {
        // Arrange
        final updatedWalkabout = testWalkabout.copyWith(
          title: 'Updated Title',
          syncStatus: SyncStatus.local,
        );

        when(
          mockLocalDataSource.updateWalkabout(any),
        ).thenAnswer((_) async => updatedWalkabout);

        // Act
        final result = await repository.updateWalkabout(testWalkabout);

        // Assert
        expect(result.syncStatus, equals(SyncStatus.local));
        verify(mockLocalDataSource.updateWalkabout(any)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.updateWalkabout(any),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.updateWalkabout(testWalkabout),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to update walkabout'),
            ),
          ),
        );
      });
    });

    group('deleteWalkabout', () {
      test('should delete walkabout successfully', () async {
        // Arrange
        when(
          mockLocalDataSource.deleteWalkabout('test-id'),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteWalkabout('test-id');

        // Assert
        expect(result, isTrue);
        verify(mockLocalDataSource.deleteWalkabout('test-id')).called(1);
      });

      test('should return false when walkabout not found', () async {
        // Arrange
        when(
          mockLocalDataSource.deleteWalkabout('non-existent'),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.deleteWalkabout('non-existent');

        // Assert
        expect(result, isFalse);
        verify(mockLocalDataSource.deleteWalkabout('non-existent')).called(1);
      });
    });

    group('updateSyncStatus', () {
      test('should update sync status successfully', () async {
        // Arrange
        final updatedWalkabout = testWalkabout.copyWith(
          syncStatus: SyncStatus.synced,
        );

        when(
          mockLocalDataSource.updateSyncStatus('test-id', SyncStatus.synced),
        ).thenAnswer((_) async => updatedWalkabout);

        // Act
        final result = await repository.updateSyncStatus(
          'test-id',
          SyncStatus.synced,
        );

        // Assert
        expect(result.syncStatus, equals(SyncStatus.synced));
        verify(
          mockLocalDataSource.updateSyncStatus('test-id', SyncStatus.synced),
        ).called(1);
      });
    });

    group('titleExistsForUser', () {
      test('should return true when title exists for user', () async {
        // Arrange
        when(
          mockLocalDataSource.titleExistsForUser('test-user-id', 'Test Title'),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.titleExistsForUser(
          'test-user-id',
          'Test Title',
        );

        // Assert
        expect(result, isTrue);
        verify(
          mockLocalDataSource.titleExistsForUser('test-user-id', 'Test Title'),
        ).called(1);
      });

      test('should return false when title does not exist for user', () async {
        // Arrange
        when(
          mockLocalDataSource.titleExistsForUser(
            'test-user-id',
            'Unique Title',
          ),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.titleExistsForUser(
          'test-user-id',
          'Unique Title',
        );

        // Assert
        expect(result, isFalse);
        verify(
          mockLocalDataSource.titleExistsForUser(
            'test-user-id',
            'Unique Title',
          ),
        ).called(1);
      });

      test('should throw exception when data source throws', () async {
        // Arrange
        when(
          mockLocalDataSource.titleExistsForUser('test-user-id', 'Test Title'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.titleExistsForUser('test-user-id', 'Test Title'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to check title existence'),
            ),
          ),
        );
      });
    });

    group('restoreWalkabout', () {
      test('should restore walkabout successfully', () async {
        // Arrange
        final restoredWalkabout = testWalkabout.copyWith(deletedAt: null);
        when(
          mockLocalDataSource.restoreWalkabout('test-id'),
        ).thenAnswer((_) async => restoredWalkabout);

        // Act
        final result = await repository.restoreWalkabout('test-id');

        // Assert
        expect(result.deletedAt, isNull);
        verify(mockLocalDataSource.restoreWalkabout('test-id')).called(1);
      });

      test('should throw exception when data source throws', () async {
        // Arrange
        when(
          mockLocalDataSource.restoreWalkabout('test-id'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.restoreWalkabout('test-id'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to restore walkabout'),
            ),
          ),
        );
      });
    });

    group('getDeletedWalkaboutsByUserId', () {
      test('should return deleted walkabouts for user', () async {
        // Arrange
        final deletedWalkabouts = [
          testWalkabout.copyWith(deletedAt: DateTime.now()),
        ];
        when(
          mockLocalDataSource.getDeletedWalkaboutsByUserId('test-user-id'),
        ).thenAnswer((_) async => deletedWalkabouts);

        // Act
        final result = await repository.getDeletedWalkaboutsByUserId(
          'test-user-id',
        );

        // Assert
        expect(result, equals(deletedWalkabouts));
        expect(result.first.deletedAt, isNotNull);
        verify(
          mockLocalDataSource.getDeletedWalkaboutsByUserId('test-user-id'),
        ).called(1);
      });

      test('should throw exception when data source throws', () async {
        // Arrange
        when(
          mockLocalDataSource.getDeletedWalkaboutsByUserId('test-user-id'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getDeletedWalkaboutsByUserId('test-user-id'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get deleted walkabouts'),
            ),
          ),
        );
      });
    });

    group('permanentlyDeleteWalkabout', () {
      test('should permanently delete walkabout successfully', () async {
        // Arrange
        when(
          mockLocalDataSource.permanentlyDeleteWalkabout('test-id'),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.permanentlyDeleteWalkabout('test-id');

        // Assert
        expect(result, isTrue);
        verify(
          mockLocalDataSource.permanentlyDeleteWalkabout('test-id'),
        ).called(1);
      });

      test('should return false when walkabout not found', () async {
        // Arrange
        when(
          mockLocalDataSource.permanentlyDeleteWalkabout('test-id'),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.permanentlyDeleteWalkabout('test-id');

        // Assert
        expect(result, isFalse);
        verify(
          mockLocalDataSource.permanentlyDeleteWalkabout('test-id'),
        ).called(1);
      });

      test('should throw exception when data source throws', () async {
        // Arrange
        when(
          mockLocalDataSource.permanentlyDeleteWalkabout('test-id'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.permanentlyDeleteWalkabout('test-id'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to permanently delete walkabout'),
            ),
          ),
        );
      });
    });
  });
}
