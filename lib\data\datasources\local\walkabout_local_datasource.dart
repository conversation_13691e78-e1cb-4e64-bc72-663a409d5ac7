import 'package:sqflite/sqflite.dart';
import '../../models/walkabout_model.dart';
import '../../../domain/entities/walkabout.dart';
import '../../../domain/entities/sync_status.dart';

/// Interface for local walkabout data operations
abstract class WalkaboutLocalDataSource {
  /// Create a new walkabout in local storage
  Future<Walkabout> createWalkabout(Walkabout walkabout);

  /// Get walkabout by ID from local storage
  Future<Walkabout?> getWalkaboutById(String id);

  /// Get all walkabouts for a specific user
  Future<List<Walkabout>> getWalkaboutsByUserId(String userId);

  /// Get walkabouts by status for a specific user
  Future<List<Walkabout>> getWalkaboutsByStatus(
    String userId,
    WalkaboutStatus status,
  );

  /// Update an existing walkabout in local storage
  Future<Walkabout> updateWalkabout(Walkabout walkabout);

  /// Delete a walkabout by ID from local storage
  Future<bool> deleteWalkabout(String id);

  /// Get walkabouts that need to be synced
  Future<List<Walkabout>> getWalkaboutsToSync();

  /// Update sync status for a walkabout
  Future<Walkabout> updateSyncStatus(String id, SyncStatus syncStatus);

  /// Check if a walkabout title exists for a specific user
  Future<bool> titleExistsForUser(String userId, String title);

  /// Restore a soft-deleted walkabout
  Future<Walkabout> restoreWalkabout(String id);

  /// Get all soft-deleted walkabouts for a specific user
  Future<List<Walkabout>> getDeletedWalkaboutsByUserId(String userId);

  /// Permanently delete a walkabout (hard delete)
  Future<bool> permanentlyDeleteWalkabout(String id);
}

/// Implementation of WalkaboutLocalDataSource using SQLite
class WalkaboutLocalDataSourceImpl implements WalkaboutLocalDataSource {
  final Database _database;

  WalkaboutLocalDataSourceImpl(this._database);

  /// Create walkabouts table in database
  static Future<void> createTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS walkabouts(
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        status TEXT NOT NULL,
        location_lat REAL,
        location_lng REAL,
        user_id TEXT NOT NULL,
        is_completed INTEGER NOT NULL DEFAULT 0,
        sync_status TEXT NOT NULL DEFAULT 'local',
        deleted_at INTEGER
      )
      ''');

    // Create indexes for performance
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_walkabouts_user_id ON walkabouts(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_walkabouts_status ON walkabouts(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_walkabouts_sync_status ON walkabouts(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_walkabouts_deleted_at ON walkabouts(deleted_at)',
    );
  }

  @override
  Future<Walkabout> createWalkabout(Walkabout walkabout) async {
    try {
      // Convert domain entity to model
      final walkaboutModel = WalkaboutModel.fromEntity(walkabout);

      // Insert into database
      await _database.insert(
        'walkabouts',
        walkaboutModel.toMap(),
        conflictAlgorithm: ConflictAlgorithm.fail,
      );

      return walkaboutModel;
    } catch (e) {
      throw Exception('Failed to create walkabout: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout?> getWalkaboutById(String id) async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) {
        return null;
      }

      return WalkaboutModel.fromMap(result.first);
    } catch (e) {
      throw Exception('Failed to get walkabout by ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsByUserId(String userId) async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'user_id = ? AND deleted_at IS NULL',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => WalkaboutModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get walkabouts by user ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsByStatus(
    String userId,
    WalkaboutStatus status,
  ) async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'user_id = ? AND status = ? AND deleted_at IS NULL',
        whereArgs: [userId, status.name],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => WalkaboutModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get walkabouts by status: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> updateWalkabout(Walkabout walkabout) async {
    try {
      // Convert domain entity to model
      final walkaboutModel = WalkaboutModel.fromEntity(walkabout);

      // Update in database
      final rowsAffected = await _database.update(
        'walkabouts',
        walkaboutModel.toMap(),
        where: 'id = ?',
        whereArgs: [walkabout.id],
      );

      if (rowsAffected == 0) {
        throw Exception('Walkabout not found');
      }

      return walkaboutModel;
    } catch (e) {
      throw Exception('Failed to update walkabout: ${e.toString()}');
    }
  }

  @override
  Future<bool> deleteWalkabout(String id) async {
    try {
      // Perform soft delete by setting deleted_at timestamp
      final now = DateTime.now().millisecondsSinceEpoch;
      final rowsAffected = await _database.update(
        'walkabouts',
        {
          'deleted_at': now,
          'updated_at': now,
          'sync_status': SyncStatus.local.name, // Mark as needing sync
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [id],
      );

      return rowsAffected > 0;
    } catch (e) {
      throw Exception('Failed to delete walkabout: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getWalkaboutsToSync() async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'sync_status != ?',
        whereArgs: ['synced'],
        orderBy: 'updated_at ASC',
      );

      return result.map((map) => WalkaboutModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get walkabouts to sync: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> updateSyncStatus(String id, SyncStatus syncStatus) async {
    try {
      // Update only sync status and updated_at
      final now = DateTime.now().millisecondsSinceEpoch;
      final rowsAffected = await _database.update(
        'walkabouts',
        {'sync_status': syncStatus.name, 'updated_at': now},
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected == 0) {
        throw Exception('Walkabout not found');
      }

      // Fetch and return updated walkabout
      final updatedWalkabout = await getWalkaboutById(id);
      if (updatedWalkabout == null) {
        throw Exception('Failed to fetch updated walkabout');
      }

      return updatedWalkabout;
    } catch (e) {
      throw Exception('Failed to update sync status: ${e.toString()}');
    }
  }

  @override
  Future<bool> titleExistsForUser(String userId, String title) async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'user_id = ? AND title = ? AND deleted_at IS NULL',
        whereArgs: [userId, title],
        limit: 1,
      );

      return result.isNotEmpty;
    } catch (e) {
      throw Exception('Failed to check title existence: ${e.toString()}');
    }
  }

  @override
  Future<Walkabout> restoreWalkabout(String id) async {
    try {
      // Restore walkabout by setting deleted_at to null
      final now = DateTime.now().millisecondsSinceEpoch;
      final rowsAffected = await _database.update(
        'walkabouts',
        {
          'deleted_at': null,
          'updated_at': now,
          'sync_status': SyncStatus.local.name, // Mark as needing sync
        },
        where: 'id = ? AND deleted_at IS NOT NULL',
        whereArgs: [id],
      );

      if (rowsAffected == 0) {
        throw Exception('Walkabout not found or not deleted');
      }

      // Fetch and return restored walkabout
      final restoredWalkabout = await getWalkaboutById(id);
      if (restoredWalkabout == null) {
        throw Exception('Failed to fetch restored walkabout');
      }

      return restoredWalkabout;
    } catch (e) {
      throw Exception('Failed to restore walkabout: ${e.toString()}');
    }
  }

  @override
  Future<List<Walkabout>> getDeletedWalkaboutsByUserId(String userId) async {
    try {
      final result = await _database.query(
        'walkabouts',
        where: 'user_id = ? AND deleted_at IS NOT NULL',
        whereArgs: [userId],
        orderBy: 'deleted_at DESC',
      );

      return result.map((map) => WalkaboutModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get deleted walkabouts: ${e.toString()}');
    }
  }

  @override
  Future<bool> permanentlyDeleteWalkabout(String id) async {
    try {
      final rowsAffected = await _database.delete(
        'walkabouts',
        where: 'id = ?',
        whereArgs: [id],
      );

      return rowsAffected > 0;
    } catch (e) {
      throw Exception(
        'Failed to permanently delete walkabout: ${e.toString()}',
      );
    }
  }
}
