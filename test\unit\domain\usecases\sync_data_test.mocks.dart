// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in safestride/test/unit/domain/usecases/sync_data_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/sync_conflict.dart' as _i4;
import 'package:safestride/domain/entities/sync_status.dart' as _i5;
import 'package:safestride/domain/repositories/sync_repository.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSyncStatistics_0 extends _i1.SmartFake
    implements _i2.SyncStatistics {
  _FakeSyncStatistics_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SyncRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSyncRepository extends _i1.Mock implements _i2.SyncRepository {
  MockSyncRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<bool> syncToRemote() =>
      (super.noSuchMethod(
            Invocation.method(#syncToRemote, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> syncFromRemote() =>
      (super.noSuchMethod(
            Invocation.method(#syncFromRemote, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> performFullSync() =>
      (super.noSuchMethod(
            Invocation.method(#performFullSync, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<List<_i4.SyncConflict>> getUnresolvedConflicts() =>
      (super.noSuchMethod(
            Invocation.method(#getUnresolvedConflicts, []),
            returnValue: _i3.Future<List<_i4.SyncConflict>>.value(
              <_i4.SyncConflict>[],
            ),
          )
          as _i3.Future<List<_i4.SyncConflict>>);

  @override
  _i3.Future<bool> resolveConflict(
    String? conflictId,
    _i4.SyncConflictResolution? resolution,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#resolveConflict, [conflictId, resolution]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Map<String, _i5.SyncStatus>> getSyncStatuses() =>
      (super.noSuchMethod(
            Invocation.method(#getSyncStatuses, []),
            returnValue: _i3.Future<Map<String, _i5.SyncStatus>>.value(
              <String, _i5.SyncStatus>{},
            ),
          )
          as _i3.Future<Map<String, _i5.SyncStatus>>);

  @override
  _i3.Future<List<String>> getEntitiesNeedingSync() =>
      (super.noSuchMethod(
            Invocation.method(#getEntitiesNeedingSync, []),
            returnValue: _i3.Future<List<String>>.value(<String>[]),
          )
          as _i3.Future<List<String>>);

  @override
  _i3.Future<void> markEntityAsSynced(
    String? entityId,
    _i4.SyncEntityType? entityType,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markEntityAsSynced, [entityId, entityType]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> markEntityAsSyncError(
    String? entityId,
    _i4.SyncEntityType? entityType,
    String? error,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markEntityAsSyncError, [
              entityId,
              entityType,
              error,
            ]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<DateTime?> getLastSyncTime() =>
      (super.noSuchMethod(
            Invocation.method(#getLastSyncTime, []),
            returnValue: _i3.Future<DateTime?>.value(),
          )
          as _i3.Future<DateTime?>);

  @override
  _i3.Future<void> updateLastSyncTime(DateTime? timestamp) =>
      (super.noSuchMethod(
            Invocation.method(#updateLastSyncTime, [timestamp]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<bool> isSyncInProgress() =>
      (super.noSuchMethod(
            Invocation.method(#isSyncInProgress, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<void> cancelSync() =>
      (super.noSuchMethod(
            Invocation.method(#cancelSync, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.SyncStatistics> getSyncStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getSyncStatistics, []),
            returnValue: _i3.Future<_i2.SyncStatistics>.value(
              _FakeSyncStatistics_0(
                this,
                Invocation.method(#getSyncStatistics, []),
              ),
            ),
          )
          as _i3.Future<_i2.SyncStatistics>);
}
