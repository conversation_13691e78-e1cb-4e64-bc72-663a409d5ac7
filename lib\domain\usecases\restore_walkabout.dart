import '../entities/walkabout.dart';
import '../repositories/walkabout_repository.dart';

/// Use case for restoring a soft-deleted walkabout
/// 
/// This use case handles the business logic for restoring walkabouts
/// that have been soft deleted.
class RestoreWalkaboutUseCase {
  final WalkaboutRepository repository;

  const RestoreWalkaboutUseCase({required this.repository});

  /// Restore a soft-deleted walkabout by ID
  /// 
  /// Takes a walkabout ID and restores it from soft-deleted state
  /// Returns the restored walkabout
  /// Throws [Exception] if restore fails or walkabout not found
  Future<Walkabout> call(String walkaboutId) async {
    // Validate input
    if (walkaboutId.trim().isEmpty) {
      throw ArgumentError('Walkabout ID cannot be empty');
    }

    // Restore walkabout through repository
    return await repository.restoreWalkabout(walkaboutId);
  }
}
