/// Synchronization status enumeration
///
/// Tracks the sync state for entities in the offline-first architecture.
/// This enum is used across walkabouts, hazards, and other syncable entities.
enum SyncStatus {
  local,
  syncing,
  synced,
  error;

  String get displayName {
    switch (this) {
      case SyncStatus.local:
        return 'Local Only';
      case SyncStatus.syncing:
        return 'Syncing';
      case SyncStatus.synced:
        return 'Synced';
      case SyncStatus.error:
        return 'Sync Error';
    }
  }

  String get description {
    switch (this) {
      case SyncStatus.local:
        return 'Data is stored locally only';
      case SyncStatus.syncing:
        return 'Data is being synchronized';
      case SyncStatus.synced:
        return 'Data is synchronized with server';
      case SyncStatus.error:
        return 'Synchronization failed';
    }
  }

  /// Check if the entity needs to be synced
  bool get needsSync {
    switch (this) {
      case SyncStatus.local:
      case SyncStatus.error:
        return true;
      case SyncStatus.syncing:
      case SyncStatus.synced:
        return false;
    }
  }

  /// Check if sync is currently in progress
  bool get isSyncing => this == SyncStatus.syncing;

  /// Check if sync completed successfully
  bool get isSynced => this == SyncStatus.synced;

  /// Check if sync failed
  bool get hasError => this == SyncStatus.error;

  /// Check if data is only available locally
  bool get isLocalOnly => this == SyncStatus.local;
}
