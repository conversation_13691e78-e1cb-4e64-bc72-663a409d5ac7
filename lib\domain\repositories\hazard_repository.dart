import '../entities/hazard.dart';
import '../entities/sync_status.dart';
import '../entities/walkabout.dart';

/// Repository interface for hazard data operations
///
/// This interface defines the contract for hazard data access
/// following Clean Architecture principles. Implementations handle
/// the actual data persistence and retrieval logic.
abstract class HazardRepository {
  /// Create a new hazard
  ///
  /// Returns the created hazard with generated ID and timestamps
  /// Throws [Exception] if creation fails
  Future<Hazard> createHazard(Hazard hazard);

  /// Get hazard by ID
  ///
  /// Returns the hazard if found, null otherwise
  /// Throws [Exception] if retrieval fails
  Future<Hazard?> getHazardById(String id);

  /// Get all hazards for a specific walkabout
  ///
  /// Returns list of hazards ordered by creation date (newest first)
  /// Returns empty list if no hazards found
  /// Throws [Exception] if retrieval fails
  Future<List<Hazard>> getHazardsByWalkaboutId(String walkaboutId);

  /// Get hazards by severity for a specific walkabout
  ///
  /// Returns list of hazards with the specified severity
  /// Returns empty list if no hazards found
  /// Throws [Exception] if retrieval fails
  Future<List<Hazard>> getHazardsBySeverity(
    String walkaboutId,
    HazardSeverity severity,
  );

  /// Get hazards by category for a specific walkabout
  ///
  /// Returns list of hazards with the specified category
  /// Returns empty list if no hazards found
  /// Throws [Exception] if retrieval fails
  Future<List<Hazard>> getHazardsByCategory(
    String walkaboutId,
    HazardCategory category,
  );

  /// Update an existing hazard
  ///
  /// Returns the updated hazard
  /// Throws [Exception] if update fails or hazard not found
  Future<Hazard> updateHazard(Hazard hazard);

  /// Delete a hazard by ID
  ///
  /// Returns true if deletion was successful, false if hazard not found
  /// Throws [Exception] if deletion fails
  Future<bool> deleteHazard(String id);

  /// Get hazards that need to be synced
  ///
  /// Returns list of hazards with sync status other than 'synced'
  /// Used for offline synchronization
  /// Returns empty list if no hazards need syncing
  /// Throws [Exception] if retrieval fails
  Future<List<Hazard>> getHazardsToSync();

  /// Update sync status for a hazard
  ///
  /// Updates only the sync status field
  /// Returns the updated hazard
  /// Throws [Exception] if update fails or hazard not found
  Future<Hazard> updateSyncStatus(String id, SyncStatus syncStatus);

  /// Search hazards by title or description
  ///
  /// Returns list of hazards matching the search query
  /// Search is case-insensitive and matches partial strings
  /// Returns empty list if no matches found
  /// Throws [Exception] if search fails
  Future<List<Hazard>> searchHazards(String walkaboutId, String query);

  /// Get hazards within a geographic area
  ///
  /// Returns list of hazards within the specified radius (in meters)
  /// from the center point
  /// Returns empty list if no hazards found in area
  /// Throws [Exception] if search fails
  Future<List<Hazard>> getHazardsInArea(
    String walkaboutId,
    GeoPoint center,
    double radiusMeters,
  );

  /// Get hazard statistics for a walkabout
  ///
  /// Returns a map with hazard counts by severity and category
  /// Useful for dashboard and reporting features
  /// Throws [Exception] if retrieval fails
  Future<Map<String, dynamic>> getHazardStatistics(String walkaboutId);
}
