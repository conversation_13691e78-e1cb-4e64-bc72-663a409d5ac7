import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/usecases/restore_walkabout.dart';
import 'package:safestride/domain/repositories/walkabout_repository.dart';

import 'create_walkabout_test.mocks.dart';

@GenerateMocks([WalkaboutRepository])
void main() {
  late RestoreWalkaboutUseCase useCase;
  late MockWalkaboutRepository mockRepository;

  setUp(() {
    mockRepository = MockWalkaboutRepository();
    useCase = RestoreWalkaboutUseCase(repository: mockRepository);
  });

  group('RestoreWalkaboutUseCase', () {
    const testWalkaboutId = 'test-walkabout-id';
    final testWalkabout = Walkabout(
      id: testWalkaboutId,
      title: 'Test Walkabout',
      description: 'Test description',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      status: WalkaboutStatus.draft,
      location: GeoPoint(latitude: 37.7749, longitude: -122.4194),
      userId: 'test-user-id',
      isCompleted: false,
      syncStatus: SyncStatus.local,
      deletedAt: null, // Restored walkabout should have null deletedAt
    );

    test('should restore walkabout successfully', () async {
      // Arrange
      when(mockRepository.restoreWalkabout(testWalkaboutId))
          .thenAnswer((_) async => testWalkabout);

      // Act
      final result = await useCase.call(testWalkaboutId);

      // Assert
      expect(result, equals(testWalkabout));
      expect(result.deletedAt, isNull);
      verify(mockRepository.restoreWalkabout(testWalkaboutId)).called(1);
    });

    test('should throw ArgumentError when walkabout ID is empty', () async {
      // Act & Assert
      expect(
        () => useCase.call(''),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Walkabout ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.restoreWalkabout(any));
    });

    test('should throw ArgumentError when walkabout ID is whitespace', () async {
      // Act & Assert
      expect(
        () => useCase.call('   '),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Walkabout ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.restoreWalkabout(any));
    });

    test('should propagate repository exceptions', () async {
      // Arrange
      when(mockRepository.restoreWalkabout(testWalkaboutId))
          .thenThrow(Exception('Walkabout not found or not deleted'));

      // Act & Assert
      expect(
        () => useCase.call(testWalkaboutId),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Walkabout not found or not deleted'),
        )),
      );
      
      verify(mockRepository.restoreWalkabout(testWalkaboutId)).called(1);
    });
  });
}
