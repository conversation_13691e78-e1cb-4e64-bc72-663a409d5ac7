import '../entities/walkabout.dart';
import '../repositories/walkabout_repository.dart';

/// Use case for soft deleting a walkabout
/// 
/// This use case handles the business logic for soft deleting walkabouts,
/// which marks them as deleted without permanently removing them from storage.
class SoftDeleteWalkaboutUseCase {
  final WalkaboutRepository repository;

  const SoftDeleteWalkaboutUseCase({required this.repository});

  /// Soft delete a walkabout by ID
  /// 
  /// Takes a walkabout ID and marks it as deleted
  /// Returns true if deletion was successful, false if walkabout not found
  /// Throws [Exception] if deletion fails
  Future<bool> call(String walkaboutId) async {
    // Validate input
    if (walkaboutId.trim().isEmpty) {
      throw ArgumentError('Walkabout ID cannot be empty');
    }

    // Perform soft delete through repository
    return await repository.deleteWalkabout(walkaboutId);
  }
}
