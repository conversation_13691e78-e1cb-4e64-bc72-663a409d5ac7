// Mocks generated by Mockito 5.4.6 from annotations
// in safestride/test/integration/hazard_documentation_permissions_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;
import 'dart:ui' as _i14;

import 'package:geolocator/geolocator.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i12;
import 'package:safestride/domain/entities/hazard.dart' as _i11;
import 'package:safestride/domain/entities/walkabout.dart' as _i8;
import 'package:safestride/domain/repositories/hazard_repository.dart' as _i4;
import 'package:safestride/domain/usecases/create_hazard.dart' as _i2;
import 'package:safestride/domain/usecases/update_hazard.dart' as _i3;
import 'package:safestride/presentation/providers/hazard_provider.dart' as _i10;
import 'package:safestride/services/camera/camera_service.dart' as _i5;
import 'package:safestride/services/location/location_service.dart' as _i6;
import 'package:safestride/services/voice/voice_input_service.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCreateHazardUseCase_0 extends _i1.SmartFake
    implements _i2.CreateHazardUseCase {
  _FakeCreateHazardUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUpdateHazardUseCase_1 extends _i1.SmartFake
    implements _i3.UpdateHazardUseCase {
  _FakeUpdateHazardUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHazardRepository_2 extends _i1.SmartFake
    implements _i4.HazardRepository {
  _FakeHazardRepository_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCameraService_3 extends _i1.SmartFake implements _i5.CameraService {
  _FakeCameraService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLocationService_4 extends _i1.SmartFake
    implements _i6.LocationService {
  _FakeLocationService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVoiceInputService_5 extends _i1.SmartFake
    implements _i7.VoiceInputService {
  _FakeVoiceInputService_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGeoPoint_6 extends _i1.SmartFake implements _i8.GeoPoint {
  _FakeGeoPoint_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLocationSettings_7 extends _i1.SmartFake
    implements _i9.LocationSettings {
  _FakeLocationSettings_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [HazardProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockHazardProvider extends _i1.Mock implements _i10.HazardProvider {
  MockHazardProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CreateHazardUseCase get createHazardUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#createHazardUseCase),
            returnValue: _FakeCreateHazardUseCase_0(
              this,
              Invocation.getter(#createHazardUseCase),
            ),
          )
          as _i2.CreateHazardUseCase);

  @override
  _i3.UpdateHazardUseCase get updateHazardUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#updateHazardUseCase),
            returnValue: _FakeUpdateHazardUseCase_1(
              this,
              Invocation.getter(#updateHazardUseCase),
            ),
          )
          as _i3.UpdateHazardUseCase);

  @override
  _i4.HazardRepository get hazardRepository =>
      (super.noSuchMethod(
            Invocation.getter(#hazardRepository),
            returnValue: _FakeHazardRepository_2(
              this,
              Invocation.getter(#hazardRepository),
            ),
          )
          as _i4.HazardRepository);

  @override
  _i5.CameraService get cameraService =>
      (super.noSuchMethod(
            Invocation.getter(#cameraService),
            returnValue: _FakeCameraService_3(
              this,
              Invocation.getter(#cameraService),
            ),
          )
          as _i5.CameraService);

  @override
  _i6.LocationService get locationService =>
      (super.noSuchMethod(
            Invocation.getter(#locationService),
            returnValue: _FakeLocationService_4(
              this,
              Invocation.getter(#locationService),
            ),
          )
          as _i6.LocationService);

  @override
  _i7.VoiceInputService get voiceInputService =>
      (super.noSuchMethod(
            Invocation.getter(#voiceInputService),
            returnValue: _FakeVoiceInputService_5(
              this,
              Invocation.getter(#voiceInputService),
            ),
          )
          as _i7.VoiceInputService);

  @override
  List<_i11.Hazard> get hazards =>
      (super.noSuchMethod(
            Invocation.getter(#hazards),
            returnValue: <_i11.Hazard>[],
          )
          as List<_i11.Hazard>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  _i11.HazardSeverity get selectedSeverity =>
      (super.noSuchMethod(
            Invocation.getter(#selectedSeverity),
            returnValue: _i11.HazardSeverity.low,
          )
          as _i11.HazardSeverity);

  @override
  _i11.HazardCategory get selectedCategory =>
      (super.noSuchMethod(
            Invocation.getter(#selectedCategory),
            returnValue: _i11.HazardCategory.slipTripFall,
          )
          as _i11.HazardCategory);

  @override
  List<String> get selectedPhotos =>
      (super.noSuchMethod(
            Invocation.getter(#selectedPhotos),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  bool get isVoiceInputActive =>
      (super.noSuchMethod(
            Invocation.getter(#isVoiceInputActive),
            returnValue: false,
          )
          as bool);

  @override
  String get voiceInputText =>
      (super.noSuchMethod(
            Invocation.getter(#voiceInputText),
            returnValue: _i12.dummyValue<String>(
              this,
              Invocation.getter(#voiceInputText),
            ),
          )
          as String);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  List<_i11.Hazard> getHazardsByWalkabout(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByWalkabout, [walkaboutId]),
            returnValue: <_i11.Hazard>[],
          )
          as List<_i11.Hazard>);

  @override
  List<_i11.Hazard> getHazardsBySeverity(
    String? walkaboutId,
    _i11.HazardSeverity? severity,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsBySeverity, [walkaboutId, severity]),
            returnValue: <_i11.Hazard>[],
          )
          as List<_i11.Hazard>);

  @override
  List<_i11.Hazard> getHazardsByCategory(
    String? walkaboutId,
    _i11.HazardCategory? category,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByCategory, [walkaboutId, category]),
            returnValue: <_i11.Hazard>[],
          )
          as List<_i11.Hazard>);

  @override
  Map<String, dynamic> getHazardStatistics(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardStatistics, [walkaboutId]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i13.Future<void> loadHazards(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#loadHazards, [walkaboutId]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<_i11.Hazard?> createHazard({
    required String? walkaboutId,
    required String? title,
    String? description,
    required _i11.HazardSeverity? severity,
    required _i11.HazardCategory? category,
    _i8.GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createHazard, [], {
              #walkaboutId: walkaboutId,
              #title: title,
              #description: description,
              #severity: severity,
              #category: category,
              #location: location,
              #photos: photos,
              #notes: notes,
            }),
            returnValue: _i13.Future<_i11.Hazard?>.value(),
          )
          as _i13.Future<_i11.Hazard?>);

  @override
  _i13.Future<_i11.Hazard?> updateHazard({
    required String? id,
    String? title,
    String? description,
    _i11.HazardSeverity? severity,
    _i11.HazardCategory? category,
    _i8.GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateHazard, [], {
              #id: id,
              #title: title,
              #description: description,
              #severity: severity,
              #category: category,
              #location: location,
              #photos: photos,
              #notes: notes,
            }),
            returnValue: _i13.Future<_i11.Hazard?>.value(),
          )
          as _i13.Future<_i11.Hazard?>);

  @override
  _i13.Future<bool> deleteHazard(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteHazard, [id]),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  void setCurrentHazard(_i11.Hazard? hazard) => super.noSuchMethod(
    Invocation.method(#setCurrentHazard, [hazard]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedSeverity(_i11.HazardSeverity? severity) => super.noSuchMethod(
    Invocation.method(#setSelectedSeverity, [severity]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedCategory(_i11.HazardCategory? category) => super.noSuchMethod(
    Invocation.method(#setSelectedCategory, [category]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedLocation(_i8.GeoPoint? location) => super.noSuchMethod(
    Invocation.method(#setSelectedLocation, [location]),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<void> getCurrentLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentLocation, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> capturePhoto() =>
      (super.noSuchMethod(
            Invocation.method(#capturePhoto, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> selectPhotoFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectPhotoFromGallery, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> selectMultiplePhotos() =>
      (super.noSuchMethod(
            Invocation.method(#selectMultiplePhotos, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void removePhoto(String? photoPath) => super.noSuchMethod(
    Invocation.method(#removePhoto, [photoPath]),
    returnValueForMissingStub: null,
  );

  @override
  void clearPhotos() => super.noSuchMethod(
    Invocation.method(#clearPhotos, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<void> startVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#startVoiceInput, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> stopVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#stopVoiceInput, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> cancelVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#cancelVoiceInput, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void clearForm() => super.noSuchMethod(
    Invocation.method(#clearForm, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<List<_i11.Hazard>> searchHazards(
    String? walkaboutId,
    String? query,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#searchHazards, [walkaboutId, query]),
            returnValue: _i13.Future<List<_i11.Hazard>>.value(<_i11.Hazard>[]),
          )
          as _i13.Future<List<_i11.Hazard>>);

  @override
  void addListener(_i14.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [CameraService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCameraService extends _i1.Mock implements _i5.CameraService {
  MockCameraService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<String?> capturePhoto() =>
      (super.noSuchMethod(
            Invocation.method(#capturePhoto, []),
            returnValue: _i13.Future<String?>.value(),
          )
          as _i13.Future<String?>);

  @override
  _i13.Future<String?> selectFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectFromGallery, []),
            returnValue: _i13.Future<String?>.value(),
          )
          as _i13.Future<String?>);

  @override
  _i13.Future<List<String>> selectMultipleFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectMultipleFromGallery, []),
            returnValue: _i13.Future<List<String>>.value(<String>[]),
          )
          as _i13.Future<List<String>>);

  @override
  _i13.Future<String> compressImage(
    String? imagePath, {
    int? targetSizeKB = 200,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #compressImage,
              [imagePath],
              {#targetSizeKB: targetSizeKB},
            ),
            returnValue: _i13.Future<String>.value(
              _i12.dummyValue<String>(
                this,
                Invocation.method(
                  #compressImage,
                  [imagePath],
                  {#targetSizeKB: targetSizeKB},
                ),
              ),
            ),
          )
          as _i13.Future<String>);

  @override
  _i13.Future<int> getImageSize(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#getImageSize, [imagePath]),
            returnValue: _i13.Future<int>.value(0),
          )
          as _i13.Future<int>);

  @override
  _i13.Future<bool> deleteImage(String? imagePath) =>
      (super.noSuchMethod(
            Invocation.method(#deleteImage, [imagePath]),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  _i13.Future<bool> isCameraAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isCameraAvailable, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  _i13.Future<bool> checkCameraPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkCameraPermission, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  _i13.Future<bool> requestCameraPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestCameraPermission, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i6.LocationService {
  MockLocationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<_i8.GeoPoint> getCurrentLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentLocation, []),
            returnValue: _i13.Future<_i8.GeoPoint>.value(
              _FakeGeoPoint_6(this, Invocation.method(#getCurrentLocation, [])),
            ),
          )
          as _i13.Future<_i8.GeoPoint>);

  @override
  _i13.Future<bool> isLocationServiceEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isLocationServiceEnabled, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  _i13.Future<_i6.LocationPermissionStatus> checkLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkLocationPermission, []),
            returnValue: _i13.Future<_i6.LocationPermissionStatus>.value(
              _i6.LocationPermissionStatus.denied,
            ),
          )
          as _i13.Future<_i6.LocationPermissionStatus>);

  @override
  _i13.Future<_i6.LocationPermissionStatus> requestLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestLocationPermission, []),
            returnValue: _i13.Future<_i6.LocationPermissionStatus>.value(
              _i6.LocationPermissionStatus.denied,
            ),
          )
          as _i13.Future<_i6.LocationPermissionStatus>);

  @override
  _i13.Future<_i8.GeoPoint> getLocationWithAccuracy(
    _i9.LocationAccuracy? accuracy,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getLocationWithAccuracy, [accuracy]),
            returnValue: _i13.Future<_i8.GeoPoint>.value(
              _FakeGeoPoint_6(
                this,
                Invocation.method(#getLocationWithAccuracy, [accuracy]),
              ),
            ),
          )
          as _i13.Future<_i8.GeoPoint>);

  @override
  double calculateDistance(_i8.GeoPoint? point1, _i8.GeoPoint? point2) =>
      (super.noSuchMethod(
            Invocation.method(#calculateDistance, [point1, point2]),
            returnValue: 0.0,
          )
          as double);

  @override
  bool isWithinRadius(
    _i8.GeoPoint? center,
    _i8.GeoPoint? point,
    double? radiusMeters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#isWithinRadius, [center, point, radiusMeters]),
            returnValue: false,
          )
          as bool);

  @override
  _i13.Future<_i9.LocationSettings> getLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getLocationSettings, []),
            returnValue: _i13.Future<_i9.LocationSettings>.value(
              _FakeLocationSettings_7(
                this,
                Invocation.method(#getLocationSettings, []),
              ),
            ),
          )
          as _i13.Future<_i9.LocationSettings>);

  @override
  _i13.Future<bool> openLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#openLocationSettings, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);
}
