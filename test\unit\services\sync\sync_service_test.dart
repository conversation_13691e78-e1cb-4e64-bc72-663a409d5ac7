import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/services/sync/sync_service.dart';
import 'package:safestride/services/sync/connectivity_service.dart';
import 'package:safestride/domain/repositories/sync_repository.dart';
import 'package:safestride/domain/usecases/sync_data.dart';

import 'sync_service_test.mocks.dart';

@GenerateMocks([
  SyncRepository,
  SyncDataUseCase,
  ConnectivityService,
])
void main() {
  late SyncService syncService;
  late MockSyncRepository mockSyncRepository;
  late MockSyncDataUseCase mockSyncDataUseCase;
  late MockConnectivityService mockConnectivityService;
  late StreamController<bool> connectivityController;

  setUp(() {
    mockSyncRepository = MockSyncRepository();
    mockSyncDataUseCase = MockSyncDataUseCase();
    mockConnectivityService = MockConnectivityService();
    connectivityController = StreamController<bool>.broadcast();

    when(mockConnectivityService.connectivityStream)
        .thenAnswer((_) => connectivityController.stream);
    when(mockConnectivityService.isConnected).thenReturn(true);
    when(mockConnectivityService.initialize()).thenAnswer((_) async {});
    when(mockConnectivityService.dispose()).thenReturn(null);

    syncService = SyncService(
      syncRepository: mockSyncRepository,
      syncDataUseCase: mockSyncDataUseCase,
      connectivityService: mockConnectivityService,
    );
  });

  tearDown(() {
    connectivityController.close();
    syncService.dispose();
  });

  group('SyncService', () {
    test('should initialize successfully', () async {
      // Act
      await syncService.initialize();

      // Assert
      verify(mockConnectivityService.initialize()).called(1);
      expect(syncService.isAutoSyncEnabled, true);
    });

    test('should trigger manual sync successfully', () async {
      // Arrange
      final mockResult = SyncResult(
        success: true,
        duration: Duration(seconds: 5),
        statistics: SyncStatistics(
          totalEntities: 10,
          syncedEntities: 10,
          pendingEntities: 0,
          errorEntities: 0,
          conflictCount: 0,
        ),
      );

      when(mockSyncDataUseCase.call(any)).thenAnswer((_) async => mockResult);
      await syncService.initialize();

      // Act
      final result = await syncService.triggerSync();

      // Assert
      expect(result.success, true);
      verify(mockSyncDataUseCase.call(any)).called(1);
    });

    test('should fail sync when no internet connection', () async {
      // Arrange
      when(mockConnectivityService.isConnected).thenReturn(false);
      await syncService.initialize();

      // Act
      final result = await syncService.triggerSync();

      // Assert
      expect(result.success, false);
      expect(result.error, 'No internet connection');
      verifyNever(mockSyncDataUseCase.call(any));
    });

    test('should prevent concurrent sync operations', () async {
      // Arrange
      final completer = Completer<SyncResult>();
      when(mockSyncDataUseCase.call(any)).thenAnswer((_) => completer.future);
      await syncService.initialize();

      // Act
      final future1 = syncService.triggerSync();
      final future2 = syncService.triggerSync();

      // Complete the first sync
      completer.complete(SyncResult(
        success: true,
        duration: Duration(seconds: 1),
      ));

      final results = await Future.wait([future1, future2]);

      // Assert
      expect(results[0].success, true);
      expect(results[1].success, false);
      expect(results[1].error, 'Sync already in progress');
    });

    test('should enable and disable auto sync', () async {
      // Arrange
      await syncService.initialize();
      final events = <SyncServiceEvent>[];
      syncService.events.listen(events.add);

      // Act
      syncService.setAutoSyncEnabled(false);
      syncService.setAutoSyncEnabled(true);

      // Wait for events to be processed
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(syncService.isAutoSyncEnabled, true);
      expect(events.any((e) => e.type == SyncServiceEventType.autoSyncDisabled), true);
      expect(events.any((e) => e.type == SyncServiceEventType.autoSyncEnabled), true);
    });

    test('should trigger sync on connectivity restored', () async {
      // Arrange
      final mockResult = SyncResult(success: true, duration: Duration(seconds: 1));
      when(mockSyncDataUseCase.call(any)).thenAnswer((_) async => mockResult);
      await syncService.initialize();

      // Act
      connectivityController.add(true); // Simulate connectivity restored
      await Future.delayed(Duration(milliseconds: 50)); // Allow async processing

      // Assert
      verify(mockSyncDataUseCase.call(any)).called(1);
    });

    test('should cancel ongoing sync', () async {
      // Arrange
      final completer = Completer<SyncResult>();
      when(mockSyncDataUseCase.call(any)).thenAnswer((_) => completer.future);
      when(mockSyncRepository.cancelSync()).thenAnswer((_) async {});
      await syncService.initialize();

      // Act
      final syncFuture = syncService.triggerSync();
      await syncService.cancelSync();

      // Complete the sync to avoid hanging test
      completer.complete(SyncResult(success: false, duration: Duration.zero));
      await syncFuture;

      // Assert
      verify(mockSyncRepository.cancelSync()).called(1);
    });

    test('should get sync status', () async {
      // Arrange
      final mockStatus = SyncStatusResult(
        statistics: SyncStatistics(
          totalEntities: 5,
          syncedEntities: 3,
          pendingEntities: 2,
          errorEntities: 0,
          conflictCount: 0,
        ),
        isInProgress: false,
      );

      when(mockSyncDataUseCase.getSyncStatus()).thenAnswer((_) async => mockStatus);
      await syncService.initialize();

      // Act
      final result = await syncService.getSyncStatus();

      // Assert
      expect(result.statistics?.totalEntities, 5);
      expect(result.isInProgress, false);
      verify(mockSyncDataUseCase.getSyncStatus()).called(1);
    });

    test('should handle sync failure and schedule retry', () async {
      // Arrange
      final mockResult = SyncResult(
        success: false,
        error: 'Network error',
        duration: Duration(seconds: 2),
      );

      when(mockSyncDataUseCase.call(any)).thenAnswer((_) async => mockResult);
      await syncService.initialize();

      final events = <SyncServiceEvent>[];
      syncService.events.listen(events.add);

      // Act
      await syncService.triggerSync();

      // Wait for retry to be scheduled
      await Future.delayed(Duration(milliseconds: 50));

      // Assert
      expect(syncService.retryAttempts, 0); // Manual sync doesn't increment retry
      expect(events.any((e) => e.type == SyncServiceEventType.syncFailed), true);
    });

    test('should emit sync events', () async {
      // Arrange
      final mockResult = SyncResult(
        success: true,
        duration: Duration(seconds: 3),
        statistics: SyncStatistics(
          totalEntities: 8,
          syncedEntities: 8,
          pendingEntities: 0,
          errorEntities: 0,
          conflictCount: 0,
        ),
      );

      when(mockSyncDataUseCase.call(any)).thenAnswer((_) async => mockResult);
      await syncService.initialize();

      final events = <SyncServiceEvent>[];
      syncService.events.listen(events.add);

      // Act
      await syncService.triggerSync();

      // Wait for events to be processed
      await Future.delayed(Duration(milliseconds: 10));

      // Assert
      expect(events.any((e) => e.type == SyncServiceEventType.syncStarted), true);
      expect(events.any((e) => e.type == SyncServiceEventType.syncCompleted), true);
    });

    test('should force sync regardless of connectivity', () async {
      // Arrange
      when(mockConnectivityService.isConnected).thenReturn(false);
      final mockResult = SyncResult(success: true, duration: Duration(seconds: 1));
      when(mockSyncDataUseCase.call(any)).thenAnswer((_) async => mockResult);
      await syncService.initialize();

      // Act
      final result = await syncService.forcSync();

      // Assert
      expect(result.success, true);
      verify(mockSyncDataUseCase.call(any)).called(1);
    });

    test('should respect auto sync disabled setting', () async {
      // Arrange
      await syncService.initialize();
      syncService.setAutoSyncEnabled(false);

      // Act
      final result = await syncService.triggerSync();

      // Assert
      expect(result.success, false);
      expect(result.error, 'Auto sync is disabled');
      verifyNever(mockSyncDataUseCase.call(any));
    });

    test('should dispose resources properly', () {
      // Act
      syncService.dispose();

      // Assert
      verify(mockConnectivityService.dispose()).called(1);
      expect(() => syncService.events.listen((_) {}), throwsA(isA<StateError>()));
    });
  });

  group('SyncServiceEvent', () {
    test('should create events with correct data', () {
      // Act
      final event = SyncServiceEvent.syncStarted(SyncType.full);

      // Assert
      expect(event.type, SyncServiceEventType.syncStarted);
      expect(event.data['syncType'], SyncType.full);
      expect(event.timestamp, isA<DateTime>());
    });

    test('should create sync completed event with statistics', () {
      // Arrange
      final statistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 8,
        pendingEntities: 2,
        errorEntities: 0,
        conflictCount: 1,
      );

      // Act
      final event = SyncServiceEvent.syncCompleted(statistics, Duration(seconds: 5));

      // Assert
      expect(event.type, SyncServiceEventType.syncCompleted);
      expect(event.data['statistics'], statistics);
      expect(event.data['duration'], Duration(seconds: 5));
    });

    test('should create sync failed event with error', () {
      // Act
      final event = SyncServiceEvent.syncFailed('Network timeout', Duration(seconds: 10));

      // Assert
      expect(event.type, SyncServiceEventType.syncFailed);
      expect(event.data['error'], 'Network timeout');
      expect(event.data['duration'], Duration(seconds: 10));
    });
  });
}
