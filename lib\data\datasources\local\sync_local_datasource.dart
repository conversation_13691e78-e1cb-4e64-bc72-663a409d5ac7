import 'package:sqflite/sqflite.dart';
import '../../../domain/entities/sync_conflict.dart';
import '../../../domain/entities/sync_status.dart';
import '../../models/sync_model.dart';

/// Interface for local sync data operations
abstract class SyncLocalDataSource {
  /// Get all entities that need to be synced
  Future<List<SyncableEntity>> getEntitiesNeedingSync();

  /// Get entities by sync status
  Future<List<SyncableEntity>> getEntitiesByStatus(SyncStatus status);

  /// Update sync status for an entity
  Future<void> updateEntitySyncStatus(
    String entityId,
    SyncEntityType entityType,
    SyncStatus status,
  );

  /// Store sync conflict
  Future<void> storeSyncConflict(SyncConflict conflict);

  /// Get all unresolved sync conflicts
  Future<List<SyncConflict>> getUnresolvedConflicts();

  /// Resolve sync conflict
  Future<void> resolveConflict(String conflictId, SyncConflictResolution resolution);

  /// Get sync metadata
  Future<SyncMetadata?> getSyncMetadata();

  /// Update sync metadata
  Future<void> updateSyncMetadata(SyncMetadata metadata);

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime();

  /// Update last sync timestamp
  Future<void> updateLastSyncTime(DateTime timestamp);

  /// Clear all sync conflicts
  Future<void> clearResolvedConflicts();

  /// Get sync statistics
  Future<Map<String, int>> getSyncStatistics();
}

/// Implementation of SyncLocalDataSource
class SyncLocalDataSourceImpl implements SyncLocalDataSource {
  final Database _database;

  SyncLocalDataSourceImpl(this._database);

  @override
  Future<List<SyncableEntity>> getEntitiesNeedingSync() async {
    final entities = <SyncableEntity>[];

    // Get walkabouts that need sync
    final walkaboutRows = await _database.query(
      'walkabouts',
      where: 'sync_status IN (?, ?) AND deleted_at IS NULL',
      whereArgs: [SyncStatus.local.name, SyncStatus.error.name],
    );

    for (final row in walkaboutRows) {
      entities.add(SyncableEntity(
        id: row['id'] as String,
        type: SyncEntityType.walkabout,
        syncStatus: SyncStatus.values.byName(row['sync_status'] as String),
        lastModified: DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int),
      ));
    }

    // Get hazards that need sync
    final hazardRows = await _database.query(
      'hazards',
      where: 'sync_status IN (?, ?)',
      whereArgs: [SyncStatus.local.name, SyncStatus.error.name],
    );

    for (final row in hazardRows) {
      entities.add(SyncableEntity(
        id: row['id'] as String,
        type: SyncEntityType.hazard,
        syncStatus: SyncStatus.values.byName(row['sync_status'] as String),
        lastModified: DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int),
      ));
    }

    return entities;
  }

  @override
  Future<List<SyncableEntity>> getEntitiesByStatus(SyncStatus status) async {
    final entities = <SyncableEntity>[];

    // Get walkabouts by status
    final walkaboutRows = await _database.query(
      'walkabouts',
      where: 'sync_status = ? AND deleted_at IS NULL',
      whereArgs: [status.name],
    );

    for (final row in walkaboutRows) {
      entities.add(SyncableEntity(
        id: row['id'] as String,
        type: SyncEntityType.walkabout,
        syncStatus: status,
        lastModified: DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int),
      ));
    }

    // Get hazards by status
    final hazardRows = await _database.query(
      'hazards',
      where: 'sync_status = ?',
      whereArgs: [status.name],
    );

    for (final row in hazardRows) {
      entities.add(SyncableEntity(
        id: row['id'] as String,
        type: SyncEntityType.hazard,
        syncStatus: status,
        lastModified: DateTime.fromMillisecondsSinceEpoch(row['updated_at'] as int),
      ));
    }

    return entities;
  }

  @override
  Future<void> updateEntitySyncStatus(
    String entityId,
    SyncEntityType entityType,
    SyncStatus status,
  ) async {
    final tableName = entityType == SyncEntityType.walkabout ? 'walkabouts' : 'hazards';
    
    await _database.update(
      tableName,
      {'sync_status': status.name},
      where: 'id = ?',
      whereArgs: [entityId],
    );
  }

  @override
  Future<void> storeSyncConflict(SyncConflict conflict) async {
    await _database.insert(
      'sync_conflicts',
      {
        'id': conflict.id,
        'entity_type': conflict.entityType.name,
        'entity_id': conflict.entityId,
        'local_data': conflict.localData.toString(),
        'remote_data': conflict.remoteData.toString(),
        'conflict_type': conflict.conflictType.name,
        'created_at': conflict.createdAt.millisecondsSinceEpoch,
        'resolved_at': conflict.resolvedAt?.millisecondsSinceEpoch,
        'resolution': conflict.resolution?.name,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<List<SyncConflict>> getUnresolvedConflicts() async {
    final rows = await _database.query(
      'sync_conflicts',
      where: 'resolved_at IS NULL',
      orderBy: 'created_at DESC',
    );

    return rows.map((row) => SyncConflictModel.fromMap(row).toDomain()).toList();
  }

  @override
  Future<void> resolveConflict(String conflictId, SyncConflictResolution resolution) async {
    await _database.update(
      'sync_conflicts',
      {
        'resolved_at': DateTime.now().millisecondsSinceEpoch,
        'resolution': resolution.name,
      },
      where: 'id = ?',
      whereArgs: [conflictId],
    );
  }

  @override
  Future<SyncMetadata?> getSyncMetadata() async {
    final rows = await _database.query(
      'sync_metadata',
      limit: 1,
    );

    if (rows.isEmpty) return null;

    return SyncMetadataModel.fromMap(rows.first).toDomain();
  }

  @override
  Future<void> updateSyncMetadata(SyncMetadata metadata) async {
    await _database.insert(
      'sync_metadata',
      SyncMetadataModel.fromDomain(metadata).toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    final metadata = await getSyncMetadata();
    return metadata?.lastSyncAt;
  }

  @override
  Future<void> updateLastSyncTime(DateTime timestamp) async {
    final existingMetadata = await getSyncMetadata();
    final updatedMetadata = existingMetadata?.copyWith(lastSyncAt: timestamp) ??
        SyncMetadata(
          id: '1',
          lastSyncAt: timestamp,
          syncVersion: 1,
          conflictCount: 0,
        );

    await updateSyncMetadata(updatedMetadata);
  }

  @override
  Future<void> clearResolvedConflicts() async {
    await _database.delete(
      'sync_conflicts',
      where: 'resolved_at IS NOT NULL AND resolved_at < ?',
      whereArgs: [DateTime.now().subtract(Duration(days: 30)).millisecondsSinceEpoch],
    );
  }

  @override
  Future<Map<String, int>> getSyncStatistics() async {
    final walkaboutStats = await _database.rawQuery('''
      SELECT sync_status, COUNT(*) as count 
      FROM walkabouts 
      WHERE deleted_at IS NULL 
      GROUP BY sync_status
    ''');

    final hazardStats = await _database.rawQuery('''
      SELECT sync_status, COUNT(*) as count 
      FROM hazards 
      GROUP BY sync_status
    ''');

    final conflictCount = await _database.rawQuery('''
      SELECT COUNT(*) as count 
      FROM sync_conflicts 
      WHERE resolved_at IS NULL
    ''');

    final stats = <String, int>{};
    
    // Process walkabout stats
    for (final row in walkaboutStats) {
      final status = row['sync_status'] as String;
      final count = row['count'] as int;
      stats['walkabout_$status'] = count;
    }

    // Process hazard stats
    for (final row in hazardStats) {
      final status = row['sync_status'] as String;
      final count = row['count'] as int;
      stats['hazard_$status'] = (stats['hazard_$status'] ?? 0) + count;
    }

    // Add conflict count
    stats['conflicts'] = conflictCount.first['count'] as int;

    return stats;
  }
}

/// Syncable entity data class
class SyncableEntity {
  final String id;
  final SyncEntityType type;
  final SyncStatus syncStatus;
  final DateTime lastModified;

  const SyncableEntity({
    required this.id,
    required this.type,
    required this.syncStatus,
    required this.lastModified,
  });
}

/// Sync metadata data class
class SyncMetadata {
  final String id;
  final DateTime? lastSyncAt;
  final int syncVersion;
  final int conflictCount;

  const SyncMetadata({
    required this.id,
    this.lastSyncAt,
    required this.syncVersion,
    required this.conflictCount,
  });

  SyncMetadata copyWith({
    String? id,
    DateTime? lastSyncAt,
    int? syncVersion,
    int? conflictCount,
  }) {
    return SyncMetadata(
      id: id ?? this.id,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      syncVersion: syncVersion ?? this.syncVersion,
      conflictCount: conflictCount ?? this.conflictCount,
    );
  }
}
