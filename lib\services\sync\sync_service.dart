import 'dart:async';
import '../../domain/repositories/sync_repository.dart';
import '../../domain/usecases/sync_data.dart';
import 'connectivity_service.dart';

/// Service for orchestrating sync operations
///
/// This service handles automatic sync triggers, retry logic,
/// and coordination between connectivity and sync operations.
class SyncService {
  final SyncRepository _syncRepository;
  final SyncDataUseCase _syncDataUseCase;
  final ConnectivityService _connectivityService;

  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _retryTimer;
  Timer? _periodicSyncTimer;

  bool _isAutoSyncEnabled = true;
  bool _isSyncInProgress = false;
  int _retryAttempts = 0;
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(minutes: 5);
  static const Duration _periodicSyncInterval = Duration(minutes: 30);

  final StreamController<SyncServiceEvent> _eventController = 
      StreamController<SyncServiceEvent>.broadcast();

  SyncService({
    required SyncRepository syncRepository,
    required SyncDataUseCase syncDataUseCase,
    required ConnectivityService connectivityService,
  })  : _syncRepository = syncRepository,
        _syncDataUseCase = syncDataUseCase,
        _connectivityService = connectivityService;

  /// Stream of sync service events
  Stream<SyncServiceEvent> get events => _eventController.stream;

  /// Check if auto sync is enabled
  bool get isAutoSyncEnabled => _isAutoSyncEnabled;

  /// Check if sync is currently in progress
  bool get isSyncInProgress => _isSyncInProgress;

  /// Get current retry attempts
  int get retryAttempts => _retryAttempts;

  /// Initialize the sync service
  Future<void> initialize() async {
    await _connectivityService.initialize();
    _startConnectivityListener();
    _startPeriodicSync();
    
    _eventController.add(SyncServiceEvent.initialized());
  }

  /// Enable or disable auto sync
  void setAutoSyncEnabled(bool enabled) {
    _isAutoSyncEnabled = enabled;
    
    if (enabled) {
      _startPeriodicSync();
      _eventController.add(SyncServiceEvent.autoSyncEnabled());
    } else {
      _stopPeriodicSync();
      _eventController.add(SyncServiceEvent.autoSyncDisabled());
    }
  }

  /// Manually trigger sync
  Future<SyncResult> triggerSync({SyncType syncType = SyncType.full}) async {
    if (!_isAutoSyncEnabled && syncType == SyncType.full) {
      return SyncResult(
        success: false,
        error: 'Auto sync is disabled',
        duration: Duration.zero,
      );
    }

    if (_isSyncInProgress) {
      return SyncResult(
        success: false,
        error: 'Sync already in progress',
        duration: Duration.zero,
      );
    }

    if (!_connectivityService.isConnected) {
      return SyncResult(
        success: false,
        error: 'No internet connection',
        duration: Duration.zero,
      );
    }

    return await _performSync(SyncParams(syncType: syncType));
  }

  /// Force sync regardless of connectivity
  Future<SyncResult> forcSync() async {
    return await _performSync(SyncParams(syncType: SyncType.full, forceSync: true));
  }

  /// Cancel ongoing sync
  Future<void> cancelSync() async {
    if (_isSyncInProgress) {
      await _syncRepository.cancelSync();
      _isSyncInProgress = false;
      _eventController.add(SyncServiceEvent.syncCancelled());
    }
  }

  /// Get sync status
  Future<SyncStatusResult> getSyncStatus() async {
    return await _syncDataUseCase.getSyncStatus();
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _retryTimer?.cancel();
    _periodicSyncTimer?.cancel();
    _eventController.close();
    _connectivityService.dispose();
  }

  /// Start listening to connectivity changes
  void _startConnectivityListener() {
    _connectivitySubscription = _connectivityService.connectivityStream.listen(
      (isConnected) async {
        if (isConnected && _isAutoSyncEnabled) {
          // Connection restored, trigger sync
          await _performAutoSync();
        }
      },
    );
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    if (!_isAutoSyncEnabled) return;
    
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = Timer.periodic(_periodicSyncInterval, (_) async {
      if (_connectivityService.isConnected && _isAutoSyncEnabled) {
        await _performAutoSync();
      }
    });
  }

  /// Stop periodic sync timer
  void _stopPeriodicSync() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = null;
  }

  /// Perform automatic sync
  Future<void> _performAutoSync() async {
    if (_isSyncInProgress) return;
    
    final result = await _performSync(SyncParams(syncType: SyncType.full));
    
    if (!result.success) {
      _scheduleRetry();
    } else {
      _retryAttempts = 0;
    }
  }

  /// Perform sync operation
  Future<SyncResult> _performSync(SyncParams params) async {
    _isSyncInProgress = true;
    _eventController.add(SyncServiceEvent.syncStarted(params.syncType));

    try {
      final result = await _syncDataUseCase.call(params);
      
      if (result.success) {
        _eventController.add(SyncServiceEvent.syncCompleted(
          result.statistics,
          result.duration,
        ));
        _retryAttempts = 0;
      } else {
        _eventController.add(SyncServiceEvent.syncFailed(
          result.error ?? 'Unknown error',
          result.duration,
        ));
        
        if (_isAutoSyncEnabled) {
          _scheduleRetry();
        }
      }
      
      return result;
    } catch (e) {
      final error = e.toString();
      _eventController.add(SyncServiceEvent.syncFailed(error, Duration.zero));
      
      if (_isAutoSyncEnabled) {
        _scheduleRetry();
      }
      
      return SyncResult(
        success: false,
        error: error,
        duration: Duration.zero,
      );
    } finally {
      _isSyncInProgress = false;
    }
  }

  /// Schedule retry for failed sync
  void _scheduleRetry() {
    if (_retryAttempts >= _maxRetryAttempts) {
      _eventController.add(SyncServiceEvent.maxRetriesReached(_retryAttempts));
      return;
    }

    _retryAttempts++;
    _retryTimer?.cancel();
    
    final delay = Duration(
      milliseconds: _retryDelay.inMilliseconds * _retryAttempts,
    );
    
    _retryTimer = Timer(delay, () async {
      if (_connectivityService.isConnected && _isAutoSyncEnabled) {
        _eventController.add(SyncServiceEvent.retryAttempt(_retryAttempts));
        await _performAutoSync();
      }
    });
  }
}

/// Sync service events
class SyncServiceEvent {
  final SyncServiceEventType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  SyncServiceEvent._(this.type, this.data) : timestamp = DateTime.now();

  factory SyncServiceEvent.initialized() => 
      SyncServiceEvent._(SyncServiceEventType.initialized, {});

  factory SyncServiceEvent.autoSyncEnabled() => 
      SyncServiceEvent._(SyncServiceEventType.autoSyncEnabled, {});

  factory SyncServiceEvent.autoSyncDisabled() => 
      SyncServiceEvent._(SyncServiceEventType.autoSyncDisabled, {});

  factory SyncServiceEvent.syncStarted(SyncType syncType) => 
      SyncServiceEvent._(SyncServiceEventType.syncStarted, {'syncType': syncType});

  factory SyncServiceEvent.syncCompleted(SyncStatistics? statistics, Duration duration) => 
      SyncServiceEvent._(SyncServiceEventType.syncCompleted, {
        'statistics': statistics,
        'duration': duration,
      });

  factory SyncServiceEvent.syncFailed(String error, Duration duration) => 
      SyncServiceEvent._(SyncServiceEventType.syncFailed, {
        'error': error,
        'duration': duration,
      });

  factory SyncServiceEvent.syncCancelled() => 
      SyncServiceEvent._(SyncServiceEventType.syncCancelled, {});

  factory SyncServiceEvent.retryAttempt(int attempt) => 
      SyncServiceEvent._(SyncServiceEventType.retryAttempt, {'attempt': attempt});

  factory SyncServiceEvent.maxRetriesReached(int attempts) => 
      SyncServiceEvent._(SyncServiceEventType.maxRetriesReached, {'attempts': attempts});

  @override
  String toString() {
    return 'SyncServiceEvent(type: $type, data: $data, timestamp: $timestamp)';
  }
}

/// Types of sync service events
enum SyncServiceEventType {
  initialized,
  autoSyncEnabled,
  autoSyncDisabled,
  syncStarted,
  syncCompleted,
  syncFailed,
  syncCancelled,
  retryAttempt,
  maxRetriesReached,
}

/// Sync service configuration
class SyncServiceConfig {
  final bool autoSyncEnabled;
  final Duration periodicSyncInterval;
  final Duration retryDelay;
  final int maxRetryAttempts;

  const SyncServiceConfig({
    this.autoSyncEnabled = true,
    this.periodicSyncInterval = const Duration(minutes: 30),
    this.retryDelay = const Duration(minutes: 5),
    this.maxRetryAttempts = 3,
  });
}
