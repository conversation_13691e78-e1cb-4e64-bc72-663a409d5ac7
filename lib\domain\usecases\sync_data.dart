import '../repositories/sync_repository.dart';
import '../entities/sync_status.dart';

/// Use case for synchronizing data between local and remote sources
///
/// This use case handles the orchestration of sync operations
/// following Clean Architecture principles.
class SyncDataUseCase {
  final SyncRepository _syncRepository;

  SyncDataUseCase(this._syncRepository);

  /// Perform full bidirectional sync
  /// Returns SyncResult with operation details
  Future<SyncResult> call([SyncParams? params]) async {
    final syncParams = params ?? const SyncParams();
    final startTime = DateTime.now();

    try {
      // Check if sync is already in progress
      if (await _syncRepository.isSyncInProgress()) {
        return SyncResult(
          success: false,
          error: 'Sync already in progress',
          duration: DateTime.now().difference(startTime),
        );
      }

      bool success = false;

      switch (syncParams.syncType) {
        case SyncType.full:
          success = await _syncRepository.performFullSync();
          break;
        case SyncType.toRemote:
          success = await _syncRepository.syncToRemote();
          break;
        case SyncType.fromRemote:
          success = await _syncRepository.syncFromRemote();
          break;
      }

      // Get updated statistics
      final statistics = await _syncRepository.getSyncStatistics();

      return SyncResult(
        success: success,
        statistics: statistics,
        duration: DateTime.now().difference(startTime),
      );
    } catch (e) {
      return SyncResult(
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      );
    }
  }

  /// Get current sync status
  Future<SyncStatusResult> getSyncStatus() async {
    try {
      final statistics = await _syncRepository.getSyncStatistics();
      final isInProgress = await _syncRepository.isSyncInProgress();
      final conflicts = await _syncRepository.getUnresolvedConflicts();

      return SyncStatusResult(
        statistics: statistics,
        isInProgress: isInProgress,
        conflictCount: conflicts.length,
      );
    } catch (e) {
      return SyncStatusResult(
        error: e.toString(),
      );
    }
  }

  /// Cancel ongoing sync operation
  Future<bool> cancelSync() async {
    try {
      await _syncRepository.cancelSync();
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Parameters for sync operation
class SyncParams {
  final SyncType syncType;
  final bool forceSync;

  const SyncParams({
    this.syncType = SyncType.full,
    this.forceSync = false,
  });
}

/// Types of sync operations
enum SyncType {
  full,
  toRemote,
  fromRemote;

  String get displayName {
    switch (this) {
      case SyncType.full:
        return 'Full Sync';
      case SyncType.toRemote:
        return 'Upload to Server';
      case SyncType.fromRemote:
        return 'Download from Server';
    }
  }
}

/// Result of sync operation
class SyncResult {
  final bool success;
  final String? error;
  final SyncStatistics? statistics;
  final Duration duration;

  const SyncResult({
    required this.success,
    this.error,
    this.statistics,
    required this.duration,
  });

  /// Check if sync completed without errors
  bool get isSuccessful => success && error == null;

  /// Get human-readable duration
  String get formattedDuration {
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    }
    return '${duration.inSeconds}s';
  }
}

/// Result of sync status query
class SyncStatusResult {
  final SyncStatistics? statistics;
  final bool isInProgress;
  final int conflictCount;
  final String? error;

  const SyncStatusResult({
    this.statistics,
    this.isInProgress = false,
    this.conflictCount = 0,
    this.error,
  });

  /// Check if there are any issues
  bool get hasIssues => error != null || conflictCount > 0 || 
      (statistics?.hasSyncIssues ?? false);
}
