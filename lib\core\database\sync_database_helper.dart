import 'package:sqflite/sqflite.dart';

/// Database helper for sync-related tables
class SyncDatabaseHelper {
  /// Create sync-related tables
  static Future<void> createSyncTables(Database db) async {
    // Create sync_conflicts table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_conflicts (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        local_data TEXT NOT NULL,
        remote_data TEXT NOT NULL,
        conflict_type TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        resolved_at INTEGER,
        resolution TEXT,
        FOREIGN KEY (entity_type, entity_id) REFERENCES walkabouts(id) ON DELETE CASCADE,
        FOREIGN KEY (entity_type, entity_id) REFERENCES hazards(id) ON DELETE CASCADE
      )
    ''');

    // Create sync_metadata table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_metadata (
        id TEXT PRIMARY KEY DEFAULT '1',
        last_sync_at INTEGER,
        sync_version INTEGER NOT NULL DEFAULT 1,
        conflict_count INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create sync_operations table for tracking sync operations
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_operations (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        created_at INTEGER NOT NULL,
        completed_at INTEGER,
        error TEXT
      )
    ''');

    // Create indexes for better performance
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_sync_conflicts_entity 
      ON sync_conflicts(entity_type, entity_id)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_sync_conflicts_resolved 
      ON sync_conflicts(resolved_at)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_sync_operations_entity 
      ON sync_operations(entity_type, entity_id)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_sync_operations_status 
      ON sync_operations(status)
    ''');

    // Insert default sync metadata if not exists
    await db.execute('''
      INSERT OR IGNORE INTO sync_metadata (id, sync_version, conflict_count) 
      VALUES ('1', 1, 0)
    ''');
  }

  /// Add sync status columns to existing tables (migration)
  static Future<void> addSyncStatusColumns(Database db) async {
    // Check if sync_status column exists in walkabouts table
    final walkaboutColumns = await db.rawQuery("PRAGMA table_info(walkabouts)");
    final hasSyncStatusWalkabout = walkaboutColumns.any((col) => col['name'] == 'sync_status');
    
    if (!hasSyncStatusWalkabout) {
      await db.execute('''
        ALTER TABLE walkabouts 
        ADD COLUMN sync_status TEXT NOT NULL DEFAULT 'local'
      ''');
    }

    // Check if sync_status column exists in hazards table
    final hazardColumns = await db.rawQuery("PRAGMA table_info(hazards)");
    final hasSyncStatusHazard = hazardColumns.any((col) => col['name'] == 'sync_status');
    
    if (!hasSyncStatusHazard) {
      await db.execute('''
        ALTER TABLE hazards 
        ADD COLUMN sync_status TEXT NOT NULL DEFAULT 'local'
      ''');
    }

    // Create indexes for sync_status columns
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_walkabouts_sync_status 
      ON walkabouts(sync_status)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_hazards_sync_status 
      ON hazards(sync_status)
    ''');
  }

  /// Drop sync-related tables (for testing or rollback)
  static Future<void> dropSyncTables(Database db) async {
    await db.execute('DROP TABLE IF EXISTS sync_conflicts');
    await db.execute('DROP TABLE IF EXISTS sync_metadata');
    await db.execute('DROP TABLE IF EXISTS sync_operations');
    
    // Drop indexes
    await db.execute('DROP INDEX IF EXISTS idx_sync_conflicts_entity');
    await db.execute('DROP INDEX IF EXISTS idx_sync_conflicts_resolved');
    await db.execute('DROP INDEX IF EXISTS idx_sync_operations_entity');
    await db.execute('DROP INDEX IF EXISTS idx_sync_operations_status');
    await db.execute('DROP INDEX IF EXISTS idx_walkabouts_sync_status');
    await db.execute('DROP INDEX IF EXISTS idx_hazards_sync_status');
  }

  /// Get sync statistics from database
  static Future<Map<String, int>> getSyncStatistics(Database db) async {
    final stats = <String, int>{};

    // Get walkabout sync statistics
    final walkaboutStats = await db.rawQuery('''
      SELECT sync_status, COUNT(*) as count 
      FROM walkabouts 
      WHERE deleted_at IS NULL 
      GROUP BY sync_status
    ''');

    for (final row in walkaboutStats) {
      final status = row['sync_status'] as String;
      final count = row['count'] as int;
      stats['walkabout_$status'] = count;
    }

    // Get hazard sync statistics
    final hazardStats = await db.rawQuery('''
      SELECT sync_status, COUNT(*) as count 
      FROM hazards 
      GROUP BY sync_status
    ''');

    for (final row in hazardStats) {
      final status = row['sync_status'] as String;
      final count = row['count'] as int;
      stats['hazard_$status'] = (stats['hazard_$status'] ?? 0) + count;
    }

    // Get conflict count
    final conflictResult = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM sync_conflicts 
      WHERE resolved_at IS NULL
    ''');

    stats['conflicts'] = conflictResult.first['count'] as int;

    // Get pending operations count
    final operationsResult = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM sync_operations 
      WHERE status = 'pending'
    ''');

    stats['pending_operations'] = operationsResult.first['count'] as int;

    return stats;
  }

  /// Clean up old resolved conflicts and completed operations
  static Future<void> cleanupOldRecords(Database db, {int daysToKeep = 30}) async {
    final cutoffTime = DateTime.now()
        .subtract(Duration(days: daysToKeep))
        .millisecondsSinceEpoch;

    // Clean up old resolved conflicts
    await db.delete(
      'sync_conflicts',
      where: 'resolved_at IS NOT NULL AND resolved_at < ?',
      whereArgs: [cutoffTime],
    );

    // Clean up old completed operations
    await db.delete(
      'sync_operations',
      where: 'status = ? AND completed_at IS NOT NULL AND completed_at < ?',
      whereArgs: ['completed', cutoffTime],
    );
  }

  /// Reset all sync statuses to local (for testing or troubleshooting)
  static Future<void> resetSyncStatuses(Database db) async {
    await db.update(
      'walkabouts',
      {'sync_status': 'local'},
      where: 'deleted_at IS NULL',
    );

    await db.update(
      'hazards',
      {'sync_status': 'local'},
    );

    // Clear sync metadata
    await db.update(
      'sync_metadata',
      {
        'last_sync_at': null,
        'sync_version': 1,
        'conflict_count': 0,
      },
      where: 'id = ?',
      whereArgs: ['1'],
    );

    // Clear all conflicts and operations
    await db.delete('sync_conflicts');
    await db.delete('sync_operations');
  }

  /// Validate sync table integrity
  static Future<bool> validateSyncTables(Database db) async {
    try {
      // Check if all required tables exist
      final tables = await db.rawQuery('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name IN ('sync_conflicts', 'sync_metadata', 'sync_operations')
      ''');

      if (tables.length != 3) {
        return false;
      }

      // Check if sync_status columns exist
      final walkaboutColumns = await db.rawQuery("PRAGMA table_info(walkabouts)");
      final hazardColumns = await db.rawQuery("PRAGMA table_info(hazards)");

      final hasSyncStatusWalkabout = walkaboutColumns.any((col) => col['name'] == 'sync_status');
      final hasSyncStatusHazard = hazardColumns.any((col) => col['name'] == 'sync_status');

      return hasSyncStatusWalkabout && hasSyncStatusHazard;
    } catch (e) {
      return false;
    }
  }

  /// Get entities that need sync
  static Future<List<Map<String, dynamic>>> getEntitiesNeedingSync(Database db) async {
    final entities = <Map<String, dynamic>>[];

    // Get walkabouts that need sync
    final walkabouts = await db.query(
      'walkabouts',
      where: 'sync_status IN (?, ?) AND deleted_at IS NULL',
      whereArgs: ['local', 'error'],
    );

    for (final walkabout in walkabouts) {
      entities.add({
        'id': walkabout['id'],
        'type': 'walkabout',
        'sync_status': walkabout['sync_status'],
        'updated_at': walkabout['updated_at'],
      });
    }

    // Get hazards that need sync
    final hazards = await db.query(
      'hazards',
      where: 'sync_status IN (?, ?)',
      whereArgs: ['local', 'error'],
    );

    for (final hazard in hazards) {
      entities.add({
        'id': hazard['id'],
        'type': 'hazard',
        'sync_status': hazard['sync_status'],
        'updated_at': hazard['updated_at'],
      });
    }

    // Sort by updated_at (oldest first for sync priority)
    entities.sort((a, b) => (a['updated_at'] as int).compareTo(b['updated_at'] as int));

    return entities;
  }
}
