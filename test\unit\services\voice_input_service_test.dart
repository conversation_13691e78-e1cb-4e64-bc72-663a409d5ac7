import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/services/voice/voice_input_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'voice_input_service_test.mocks.dart';

@GenerateMocks([Connectivity])
void main() {
  group('VoiceInputService Tests', () {
    late VoiceInputServiceImpl voiceInputService;
    late MockConnectivity mockConnectivity;

    setUp(() {
      mockConnectivity = MockConnectivity();
      voiceInputService = VoiceInputServiceImpl();
    });

    group('Offline Functionality Tests', () {
      test('should initialize successfully when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        // Act
        final result = await voiceInputService.initialize();

        // Assert
        expect(result, isTrue);
        expect(voiceInputService.isListening, isFalse);
      });

      test('should be available when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        // Act
        final available = await voiceInputService.isAvailable();

        // Assert
        expect(available, isTrue);
      });

      test('should handle offline voice input gracefully', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        String? recognizedText;
        String? errorMessage;

        // Act
        final started = await voiceInputService.startListening(
          onResult: (text) => recognizedText = text,
          onError: (error) => errorMessage = error,
        );

        // Wait for simulated voice input
        await Future.delayed(const Duration(seconds: 3));

        // Assert
        expect(started, isTrue);
        expect(recognizedText, isNotNull);
        expect(recognizedText, equals('Sample voice input text'));
        expect(errorMessage, isNull);
        expect(voiceInputService.lastRecognizedText, equals('Sample voice input text'));
        expect(voiceInputService.lastConfidenceLevel, equals(0.8));
      });

      test('should provide fallback locales when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        // Act
        final locales = await voiceInputService.getAvailableLocales();

        // Assert
        expect(locales, isNotEmpty);
        expect(locales, contains('en-US'));
        expect(locales, contains('en-GB'));
        expect(locales.length, greaterThanOrEqualTo(4));
      });

      test('should handle microphone permissions when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        // Act
        final hasPermission = await voiceInputService.checkMicrophonePermission();
        final requestResult = await voiceInputService.requestMicrophonePermission();

        // Assert
        expect(hasPermission, isTrue);
        expect(requestResult, isTrue);
      });

      test('should stop listening when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        await voiceInputService.startListening();
        expect(voiceInputService.isListening, isTrue);

        // Act
        await voiceInputService.stopListening();

        // Assert
        expect(voiceInputService.isListening, isFalse);
      });

      test('should cancel listening when offline', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        await voiceInputService.startListening();
        expect(voiceInputService.isListening, isTrue);

        // Act
        await voiceInputService.cancelListening();

        // Assert
        expect(voiceInputService.isListening, isFalse);
        expect(voiceInputService.lastRecognizedText, isEmpty);
        expect(voiceInputService.lastConfidenceLevel, equals(0.0));
      });
    });

    group('Online vs Offline Behavior Tests', () {
      test('should work consistently online and offline', () async {
        // Test online behavior
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.wifi);

        final onlineAvailable = await voiceInputService.isAvailable();
        expect(onlineAvailable, isTrue);

        // Test offline behavior
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        final offlineAvailable = await voiceInputService.isAvailable();
        expect(offlineAvailable, isTrue);

        // Should behave the same
        expect(onlineAvailable, equals(offlineAvailable));
      });

      test('should handle network transitions gracefully', () async {
        // Start online
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.wifi);

        await voiceInputService.initialize();
        expect(await voiceInputService.isAvailable(), isTrue);

        // Simulate going offline during listening
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);

        String? recognizedText;
        final started = await voiceInputService.startListening(
          onResult: (text) => recognizedText = text,
        );

        expect(started, isTrue);
        
        // Wait for result
        await Future.delayed(const Duration(seconds: 3));
        
        // Should still work offline
        expect(recognizedText, isNotNull);
        expect(voiceInputService.lastRecognizedText, isNotEmpty);
      });
    });

    group('Error Handling Tests', () {
      test('should handle initialization errors gracefully', () async {
        // This test verifies that the service handles errors without crashing
        // In a real implementation, this might test actual speech recognition failures
        
        // Act & Assert - should not throw
        expect(() => voiceInputService.initialize(), returnsNormally);
      });

      test('should handle listening errors gracefully', () async {
        // Arrange
        String? errorMessage;

        // Act
        await voiceInputService.startListening(
          onError: (error) => errorMessage = error,
        );

        // Simulate an error scenario by trying to start listening again
        await voiceInputService.startListening(
          onError: (error) => errorMessage = error,
        );

        // Assert - should handle gracefully without throwing
        expect(errorMessage, isNull); // No error in placeholder implementation
      });
    });

    group('Voice Input Utility Tests', () {
      test('should check confidence levels correctly', () {
        // Test acceptable confidence levels
        expect(VoiceInputUtils.isConfidenceAcceptable(0.8), isTrue);
        expect(VoiceInputUtils.isConfidenceAcceptable(0.5), isTrue);
        expect(VoiceInputUtils.isConfidenceAcceptable(0.6), isTrue);

        // Test unacceptable confidence levels
        expect(VoiceInputUtils.isConfidenceAcceptable(0.4), isFalse);
        expect(VoiceInputUtils.isConfidenceAcceptable(0.1), isFalse);
        expect(VoiceInputUtils.isConfidenceAcceptable(0.0), isFalse);
      });

      test('should clean recognized text properly', () {
        // Test text cleaning
        expect(VoiceInputUtils.cleanRecognizedText('  hello   world  '), equals('hello world'));
        expect(VoiceInputUtils.cleanRecognizedText('text\twith\ttabs'), equals('text with tabs'));
        expect(VoiceInputUtils.cleanRecognizedText('multiple    spaces'), equals('multiple spaces'));
        expect(VoiceInputUtils.cleanRecognizedText(''), equals(''));
      });

      test('should capitalize text correctly', () {
        // Test text capitalization
        expect(VoiceInputUtils.capitalizeText('hello world'), equals('Hello world'));
        expect(VoiceInputUtils.capitalizeText('HELLO WORLD'), equals('HELLO WORLD'));
        expect(VoiceInputUtils.capitalizeText('h'), equals('H'));
        expect(VoiceInputUtils.capitalizeText(''), equals(''));
      });

      test('should format text for hazard descriptions', () {
        // Test complete formatting
        expect(
          VoiceInputUtils.formatForHazardDescription('  spill   on   floor  '),
          equals('Spill on floor'),
        );
        expect(
          VoiceInputUtils.formatForHazardDescription('blocked emergency exit'),
          equals('Blocked emergency exit'),
        );
      });

      test('should provide user-friendly error messages', () {
        // Test error message formatting
        expect(
          VoiceInputUtils.getUserFriendlyErrorMessage('permission denied'),
          equals('Microphone permission is required for voice input'),
        );
        expect(
          VoiceInputUtils.getUserFriendlyErrorMessage('network error occurred'),
          equals('Network connection required for speech recognition'),
        );
        expect(
          VoiceInputUtils.getUserFriendlyErrorMessage('request timeout'),
          equals('Speech recognition timed out. Please try again'),
        );
        expect(
          VoiceInputUtils.getUserFriendlyErrorMessage('no_match found'),
          equals('Could not understand speech. Please try again'),
        );
        expect(
          VoiceInputUtils.getUserFriendlyErrorMessage('unknown error'),
          equals('Voice input failed. Please try again'),
        );
      });
    });

    group('State Management Tests', () {
      test('should track listening state correctly', () async {
        // Initially not listening
        expect(voiceInputService.isListening, isFalse);

        // Start listening
        await voiceInputService.startListening();
        expect(voiceInputService.isListening, isTrue);

        // Stop listening
        await voiceInputService.stopListening();
        expect(voiceInputService.isListening, isFalse);
      });

      test('should reset state on cancel', () async {
        // Start listening and set some state
        await voiceInputService.startListening();
        
        // Wait for simulated result
        await Future.delayed(const Duration(seconds: 3));
        
        expect(voiceInputService.lastRecognizedText, isNotEmpty);
        expect(voiceInputService.lastConfidenceLevel, greaterThan(0));

        // Cancel listening
        await voiceInputService.cancelListening();

        // State should be reset
        expect(voiceInputService.isListening, isFalse);
        expect(voiceInputService.lastRecognizedText, isEmpty);
        expect(voiceInputService.lastConfidenceLevel, equals(0.0));
      });

      test('should handle multiple start/stop cycles', () async {
        // Multiple cycles should work without issues
        for (int i = 0; i < 3; i++) {
          await voiceInputService.startListening();
          expect(voiceInputService.isListening, isTrue);
          
          await voiceInputService.stopListening();
          expect(voiceInputService.isListening, isFalse);
        }
      });
    });
  });
}
