import '../entities/hazard.dart';
import '../entities/walkabout.dart';
import '../entities/sync_status.dart';
import '../repositories/hazard_repository.dart';

/// Use case for updating an existing hazard
///
/// This use case encapsulates the business logic for hazard updates
/// following Clean Architecture principles.
class UpdateHazardUseCase {
  final HazardRepository repository;

  const UpdateHazardUseCase({required this.repository});

  /// Execute the update hazard use case
  ///
  /// Takes a [UpdateHazardParams] object containing the updated hazard details
  /// Returns the updated hazard with new timestamp
  /// Throws [Exception] if update fails or validation errors occur
  Future<Hazard> call(UpdateHazardParams params) async {
    // Validate input parameters
    _validateParams(params);

    // Get existing hazard
    final existingHazard = await repository.getHazardById(params.id);
    if (existingHazard == null) {
      throw ArgumentError('Hazard with ID ${params.id} not found');
    }

    // Create updated hazard with new timestamp
    final updatedHazard = existingHazard.copyWith(
      title: params.title,
      description: params.description,
      severity: params.severity,
      category: params.category,
      location: params.location,
      photos: params.photos,
      notes: params.notes,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.local, // Mark as needing sync
    );

    // Save updated hazard through repository
    return await repository.updateHazard(updatedHazard);
  }
}

/// Use case for updating hazard photos
///
/// Specialized use case for managing hazard photo attachments
class UpdateHazardPhotosUseCase {
  final HazardRepository repository;

  const UpdateHazardPhotosUseCase({required this.repository});

  /// Add photos to an existing hazard
  Future<Hazard> addPhotos(String hazardId, List<String> newPhotos) async {
    final existingHazard = await repository.getHazardById(hazardId);
    if (existingHazard == null) {
      throw ArgumentError('Hazard with ID $hazardId not found');
    }

    final updatedPhotos = List<String>.from(existingHazard.photos)
      ..addAll(newPhotos);

    final updatedHazard = existingHazard.copyWith(
      photos: updatedPhotos,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.local,
    );

    return await repository.updateHazard(updatedHazard);
  }

  /// Remove photos from an existing hazard
  Future<Hazard> removePhotos(
    String hazardId,
    List<String> photosToRemove,
  ) async {
    final existingHazard = await repository.getHazardById(hazardId);
    if (existingHazard == null) {
      throw ArgumentError('Hazard with ID $hazardId not found');
    }

    final updatedPhotos = List<String>.from(existingHazard.photos)
      ..removeWhere((photo) => photosToRemove.contains(photo));

    final updatedHazard = existingHazard.copyWith(
      photos: updatedPhotos,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.local,
    );

    return await repository.updateHazard(updatedHazard);
  }
}

/// Validate input parameters for hazard updates
void _validateParams(UpdateHazardParams params) {
  if (params.id.trim().isEmpty) {
    throw ArgumentError('Hazard ID is required');
  }

  if (params.title != null) {
    if (params.title!.trim().isEmpty) {
      throw ArgumentError('Hazard title cannot be empty');
    }

    if (params.title!.length > 100) {
      throw ArgumentError('Hazard title cannot exceed 100 characters');
    }
  }

  if (params.description != null && params.description!.length > 1000) {
    throw ArgumentError('Hazard description cannot exceed 1000 characters');
  }

  if (params.notes != null && params.notes!.length > 500) {
    throw ArgumentError('Hazard notes cannot exceed 500 characters');
  }

  // Validate location coordinates if provided
  if (params.location != null) {
    final lat = params.location!.latitude;
    final lng = params.location!.longitude;

    if (lat < -90 || lat > 90) {
      throw ArgumentError('Invalid latitude: must be between -90 and 90');
    }

    if (lng < -180 || lng > 180) {
      throw ArgumentError('Invalid longitude: must be between -180 and 180');
    }
  }

  // Validate photo paths
  if (params.photos != null) {
    for (final photo in params.photos!) {
      if (photo.trim().isEmpty) {
        throw ArgumentError('Photo path cannot be empty');
      }
    }
  }
}

/// Parameters for updating a hazard
class UpdateHazardParams {
  final String id;
  final String? title;
  final String? description;
  final HazardSeverity? severity;
  final HazardCategory? category;
  final GeoPoint? location;
  final List<String>? photos;
  final String? notes;

  const UpdateHazardParams({
    required this.id,
    this.title,
    this.description,
    this.severity,
    this.category,
    this.location,
    this.photos,
    this.notes,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpdateHazardParams &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.severity == severity &&
        other.category == category &&
        other.location == location &&
        _listEquals(other.photos, photos) &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      severity,
      category,
      location,
      photos != null ? Object.hashAll(photos!) : null,
      notes,
    );
  }

  @override
  String toString() {
    return 'UpdateHazardParams(id: $id, title: $title, '
        'description: $description, severity: $severity, category: $category, '
        'location: $location, photos: $photos, notes: $notes)';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
