import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../domain/entities/sync_conflict.dart';
import '../../../domain/entities/walkabout.dart';
import '../../../domain/entities/hazard.dart';
import '../../models/walkabout_model.dart';
import '../../models/hazard_model.dart';

/// Interface for remote sync data operations
abstract class SyncRemoteDataSource {
  /// Upload walkabout to remote
  Future<bool> uploadWalkabout(Walkabout walkabout);

  /// Upload hazard to remote
  Future<bool> uploadHazard(Hazard hazard);

  /// Download walkabouts from remote
  Future<List<Walkabout>> downloadWalkabouts(String userId, DateTime? lastSyncTime);

  /// Download hazards from remote
  Future<List<Hazard>> downloadHazards(String walkaboutId, DateTime? lastSyncTime);

  /// Delete walkabout from remote
  Future<bool> deleteWalkabout(String walkaboutId);

  /// Delete hazard from remote
  Future<bool> deleteHazard(String hazardId);

  /// Check if entity exists remotely
  Future<bool> entityExists(String entityId, SyncEntityType entityType);

  /// Get remote entity timestamp
  Future<DateTime?> getRemoteEntityTimestamp(String entityId, SyncEntityType entityType);

  /// Batch upload entities
  Future<Map<String, bool>> batchUpload(List<dynamic> entities);

  /// Get server timestamp
  Future<DateTime> getServerTimestamp();
}

/// Implementation of SyncRemoteDataSource using Firebase Firestore
class SyncRemoteDataSourceImpl implements SyncRemoteDataSource {
  final FirebaseFirestore _firestore;

  SyncRemoteDataSourceImpl({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<bool> uploadWalkabout(Walkabout walkabout) async {
    try {
      final walkaboutModel = WalkaboutModel.fromDomain(walkabout);
      final data = walkaboutModel.toJson();
      
      // Add server timestamp
      data['serverUpdatedAt'] = FieldValue.serverTimestamp();
      
      await _firestore
          .collection('walkabouts')
          .doc(walkabout.id)
          .set(data, SetOptions(merge: true));
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> uploadHazard(Hazard hazard) async {
    try {
      final hazardModel = HazardModel.fromDomain(hazard);
      final data = hazardModel.toJson();
      
      // Add server timestamp
      data['serverUpdatedAt'] = FieldValue.serverTimestamp();
      
      await _firestore
          .collection('hazards')
          .doc(hazard.id)
          .set(data, SetOptions(merge: true));
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<Walkabout>> downloadWalkabouts(String userId, DateTime? lastSyncTime) async {
    try {
      Query query = _firestore
          .collection('walkabouts')
          .where('userId', isEqualTo: userId);

      if (lastSyncTime != null) {
        query = query.where('serverUpdatedAt', isGreaterThan: Timestamp.fromDate(lastSyncTime));
      }

      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => WalkaboutModel.fromJson({
                ...doc.data() as Map<String, dynamic>,
                'id': doc.id,
              }).toDomain())
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<Hazard>> downloadHazards(String walkaboutId, DateTime? lastSyncTime) async {
    try {
      Query query = _firestore
          .collection('hazards')
          .where('walkaboutId', isEqualTo: walkaboutId);

      if (lastSyncTime != null) {
        query = query.where('serverUpdatedAt', isGreaterThan: Timestamp.fromDate(lastSyncTime));
      }

      final snapshot = await query.get();
      
      return snapshot.docs
          .map((doc) => HazardModel.fromJson({
                ...doc.data() as Map<String, dynamic>,
                'id': doc.id,
              }).toDomain())
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> deleteWalkabout(String walkaboutId) async {
    try {
      // Soft delete by updating deletedAt field
      await _firestore
          .collection('walkabouts')
          .doc(walkaboutId)
          .update({
            'deletedAt': FieldValue.serverTimestamp(),
            'serverUpdatedAt': FieldValue.serverTimestamp(),
          });
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteHazard(String hazardId) async {
    try {
      // Hard delete for hazards
      await _firestore
          .collection('hazards')
          .doc(hazardId)
          .delete();
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> entityExists(String entityId, SyncEntityType entityType) async {
    try {
      final collection = entityType == SyncEntityType.walkabout ? 'walkabouts' : 'hazards';
      final doc = await _firestore.collection(collection).doc(entityId).get();
      return doc.exists;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<DateTime?> getRemoteEntityTimestamp(String entityId, SyncEntityType entityType) async {
    try {
      final collection = entityType == SyncEntityType.walkabout ? 'walkabouts' : 'hazards';
      final doc = await _firestore.collection(collection).doc(entityId).get();
      
      if (!doc.exists) return null;
      
      final data = doc.data() as Map<String, dynamic>;
      final timestamp = data['serverUpdatedAt'] as Timestamp?;
      
      return timestamp?.toDate();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<Map<String, bool>> batchUpload(List<dynamic> entities) async {
    final results = <String, bool>{};
    final batch = _firestore.batch();
    
    try {
      for (final entity in entities) {
        if (entity is Walkabout) {
          final model = WalkaboutModel.fromDomain(entity);
          final data = model.toJson();
          data['serverUpdatedAt'] = FieldValue.serverTimestamp();
          
          final docRef = _firestore.collection('walkabouts').doc(entity.id);
          batch.set(docRef, data, SetOptions(merge: true));
          results[entity.id] = true;
        } else if (entity is Hazard) {
          final model = HazardModel.fromDomain(entity);
          final data = model.toJson();
          data['serverUpdatedAt'] = FieldValue.serverTimestamp();
          
          final docRef = _firestore.collection('hazards').doc(entity.id);
          batch.set(docRef, data, SetOptions(merge: true));
          results[entity.id] = true;
        }
      }
      
      await batch.commit();
      return results;
    } catch (e) {
      // Mark all as failed
      for (final key in results.keys) {
        results[key] = false;
      }
      return results;
    }
  }

  @override
  Future<DateTime> getServerTimestamp() async {
    try {
      // Create a temporary document to get server timestamp
      final docRef = _firestore.collection('_temp').doc();
      await docRef.set({'timestamp': FieldValue.serverTimestamp()});
      
      final doc = await docRef.get();
      await docRef.delete(); // Clean up
      
      final timestamp = doc.data()?['timestamp'] as Timestamp?;
      return timestamp?.toDate() ?? DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Get all walkabouts for a user with conflict detection
  Future<List<WalkaboutConflictInfo>> getWalkaboutsWithConflictInfo(
    String userId,
    List<String> localWalkaboutIds,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('walkabouts')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        final remoteTimestamp = (data['serverUpdatedAt'] as Timestamp?)?.toDate();
        
        return WalkaboutConflictInfo(
          id: doc.id,
          remoteData: data,
          remoteTimestamp: remoteTimestamp,
          existsLocally: localWalkaboutIds.contains(doc.id),
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get all hazards for walkabouts with conflict detection
  Future<List<HazardConflictInfo>> getHazardsWithConflictInfo(
    List<String> walkaboutIds,
    List<String> localHazardIds,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('hazards')
          .where('walkaboutId', whereIn: walkaboutIds)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        final remoteTimestamp = (data['serverUpdatedAt'] as Timestamp?)?.toDate();
        
        return HazardConflictInfo(
          id: doc.id,
          walkaboutId: data['walkaboutId'] as String,
          remoteData: data,
          remoteTimestamp: remoteTimestamp,
          existsLocally: localHazardIds.contains(doc.id),
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }
}

/// Conflict info for walkabouts
class WalkaboutConflictInfo {
  final String id;
  final Map<String, dynamic> remoteData;
  final DateTime? remoteTimestamp;
  final bool existsLocally;

  const WalkaboutConflictInfo({
    required this.id,
    required this.remoteData,
    this.remoteTimestamp,
    required this.existsLocally,
  });
}

/// Conflict info for hazards
class HazardConflictInfo {
  final String id;
  final String walkaboutId;
  final Map<String, dynamic> remoteData;
  final DateTime? remoteTimestamp;
  final bool existsLocally;

  const HazardConflictInfo({
    required this.id,
    required this.walkaboutId,
    required this.remoteData,
    this.remoteTimestamp,
    required this.existsLocally,
  });
}
