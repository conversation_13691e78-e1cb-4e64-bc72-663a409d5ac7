import 'dart:convert';
import '../../domain/entities/hazard.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/entities/walkabout.dart';

/// Data model for Hazard entity
///
/// This model handles serialization/deserialization for different data sources
/// following Clean Architecture principles.
class HazardModel extends Hazard {
  const HazardModel({
    required super.id,
    required super.walkaboutId,
    required super.title,
    super.description,
    required super.severity,
    required super.category,
    super.location,
    required super.photos,
    super.notes,
    required super.createdAt,
    required super.updatedAt,
    required super.syncStatus,
  });

  /// Create HazardModel from domain entity
  factory HazardModel.fromEntity(Hazard hazard) {
    return HazardModel(
      id: hazard.id,
      walkaboutId: hazard.walkaboutId,
      title: hazard.title,
      description: hazard.description,
      severity: hazard.severity,
      category: hazard.category,
      location: hazard.location,
      photos: hazard.photos,
      notes: hazard.notes,
      createdAt: hazard.createdAt,
      updatedAt: hazard.updatedAt,
      syncStatus: hazard.syncStatus,
    );
  }

  /// Create HazardModel from SQLite map
  factory HazardModel.fromMap(Map<String, dynamic> map) {
    return HazardModel(
      id: map['id'] as String,
      walkaboutId: map['walkabout_id'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      severity: _mapStringToHazardSeverity(map['severity'] as String),
      category: _mapStringToHazardCategory(map['category'] as String),
      location:
          map['location_lat'] != null && map['location_lng'] != null
              ? GeoPoint(
                latitude: map['location_lat'] as double,
                longitude: map['location_lng'] as double,
              )
              : null,
      photos: _mapStringToPhotoList(map['photos'] as String?),
      notes: map['notes'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      syncStatus: _mapStringToSyncStatus(map['sync_status'] as String),
    );
  }

  /// Create HazardModel from JSON map (for Firebase/API)
  factory HazardModel.fromJson(Map<String, dynamic> json) {
    return HazardModel(
      id: json['id'] as String,
      walkaboutId: json['walkaboutId'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      severity: _mapStringToHazardSeverity(json['severity'] as String),
      category: _mapStringToHazardCategory(json['category'] as String),
      location:
          json['location'] != null
              ? GeoPoint(
                latitude: json['location']['latitude'] as double,
                longitude: json['location']['longitude'] as double,
              )
              : null,
      photos: (json['photos'] as List<dynamic>?)?.cast<String>() ?? [],
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      syncStatus: _mapStringToSyncStatus(json['syncStatus'] as String),
    );
  }

  /// Convert to SQLite map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'walkabout_id': walkaboutId,
      'title': title,
      'description': description,
      'severity': severity.name,
      'category': category.name,
      'location_lat': location?.latitude,
      'location_lng': location?.longitude,
      'photos': _mapPhotoListToString(photos),
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'sync_status': syncStatus.name,
    };
  }

  /// Convert to JSON map (for Firebase/API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'walkaboutId': walkaboutId,
      'title': title,
      'description': description,
      'severity': severity.name,
      'category': category.name,
      'location':
          location != null
              ? {
                'latitude': location!.latitude,
                'longitude': location!.longitude,
              }
              : null,
      'photos': photos,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncStatus': syncStatus.name,
    };
  }

  /// Helper method to convert string to HazardSeverity
  static HazardSeverity _mapStringToHazardSeverity(String severity) {
    switch (severity) {
      case 'low':
        return HazardSeverity.low;
      case 'medium':
        return HazardSeverity.medium;
      case 'high':
        return HazardSeverity.high;
      case 'critical':
        return HazardSeverity.critical;
      default:
        throw ArgumentError('Invalid hazard severity: $severity');
    }
  }

  /// Helper method to convert string to HazardCategory
  static HazardCategory _mapStringToHazardCategory(String category) {
    switch (category) {
      case 'slipTripFall':
        return HazardCategory.slipTripFall;
      case 'electrical':
        return HazardCategory.electrical;
      case 'chemical':
        return HazardCategory.chemical;
      case 'fire':
        return HazardCategory.fire;
      case 'machinery':
        return HazardCategory.machinery;
      case 'ergonomic':
        return HazardCategory.ergonomic;
      case 'environmental':
        return HazardCategory.environmental;
      case 'biological':
        return HazardCategory.biological;
      case 'radiation':
        return HazardCategory.radiation;
      case 'other':
        return HazardCategory.other;
      default:
        throw ArgumentError('Invalid hazard category: $category');
    }
  }

  /// Helper method to convert string to SyncStatus
  static SyncStatus _mapStringToSyncStatus(String syncStatus) {
    switch (syncStatus) {
      case 'local':
        return SyncStatus.local;
      case 'syncing':
        return SyncStatus.syncing;
      case 'synced':
        return SyncStatus.synced;
      case 'error':
        return SyncStatus.error;
      default:
        throw ArgumentError('Invalid sync status: $syncStatus');
    }
  }

  /// Helper method to convert photo list to JSON string
  static String _mapPhotoListToString(List<String> photos) {
    return jsonEncode(photos);
  }

  /// Helper method to convert JSON string to photo list
  static List<String> _mapStringToPhotoList(String? photosJson) {
    if (photosJson == null || photosJson.isEmpty) {
      return [];
    }
    try {
      final List<dynamic> decoded = jsonDecode(photosJson);
      return decoded.cast<String>();
    } catch (e) {
      return [];
    }
  }

  @override
  String toString() {
    return 'HazardModel(id: $id, walkaboutId: $walkaboutId, title: $title, '
        'severity: $severity, category: $category, syncStatus: $syncStatus)';
  }
}
