import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/data/repositories/sync_repository_impl.dart';
import 'package:safestride/data/datasources/local/sync_local_datasource.dart';
import 'package:safestride/data/datasources/remote/sync_remote_datasource.dart';
import 'package:safestride/domain/repositories/walkabout_repository.dart';
import 'package:safestride/domain/repositories/hazard_repository.dart';
import 'package:safestride/domain/entities/sync_conflict.dart';
import 'package:safestride/domain/entities/sync_status.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/entities/hazard.dart';

import 'sync_repository_test.mocks.dart';

@GenerateMocks([
  SyncLocalDataSource,
  SyncRemoteDataSource,
  WalkaboutRepository,
  HazardRepository,
])
void main() {
  late SyncRepositoryImpl repository;
  late MockSyncLocalDataSource mockLocalDataSource;
  late MockSyncRemoteDataSource mockRemoteDataSource;
  late MockWalkaboutRepository mockWalkaboutRepository;
  late MockHazardRepository mockHazardRepository;

  setUp(() {
    mockLocalDataSource = MockSyncLocalDataSource();
    mockRemoteDataSource = MockSyncRemoteDataSource();
    mockWalkaboutRepository = MockWalkaboutRepository();
    mockHazardRepository = MockHazardRepository();
    
    repository = SyncRepositoryImpl(
      localDataSource: mockLocalDataSource,
      remoteDataSource: mockRemoteDataSource,
      walkaboutRepository: mockWalkaboutRepository,
      hazardRepository: mockHazardRepository,
    );
  });

  group('SyncRepositoryImpl', () {
    final testWalkabout = Walkabout(
      id: 'walkabout1',
      title: 'Test Walkabout',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      status: WalkaboutStatus.draft,
      userId: 'user1',
      isCompleted: false,
      syncStatus: SyncStatus.local,
    );

    final testHazard = Hazard(
      id: 'hazard1',
      walkaboutId: 'walkabout1',
      title: 'Test Hazard',
      severity: HazardSeverity.medium,
      category: HazardCategory.other,
      photos: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.local,
    );

    test('should sync to remote successfully', () async {
      // Arrange
      final entities = [
        SyncableEntity(
          id: 'walkabout1',
          type: SyncEntityType.walkabout,
          syncStatus: SyncStatus.local,
          lastModified: DateTime.now(),
        ),
      ];

      when(mockLocalDataSource.getEntitiesNeedingSync())
          .thenAnswer((_) async => entities);
      when(mockLocalDataSource.updateEntitySyncStatus(any, any, any))
          .thenAnswer((_) async {});
      when(mockWalkaboutRepository.getWalkaboutById('walkabout1'))
          .thenAnswer((_) async => testWalkabout);
      when(mockRemoteDataSource.uploadWalkabout(testWalkabout))
          .thenAnswer((_) async => true);

      // Act
      final result = await repository.syncToRemote();

      // Assert
      expect(result, true);
      verify(mockLocalDataSource.updateEntitySyncStatus(
        'walkabout1',
        SyncEntityType.walkabout,
        SyncStatus.syncing,
      )).called(1);
      verify(mockLocalDataSource.updateEntitySyncStatus(
        'walkabout1',
        SyncEntityType.walkabout,
        SyncStatus.synced,
      )).called(1);
      verify(mockRemoteDataSource.uploadWalkabout(testWalkabout)).called(1);
    });

    test('should handle sync to remote failure', () async {
      // Arrange
      final entities = [
        SyncableEntity(
          id: 'hazard1',
          type: SyncEntityType.hazard,
          syncStatus: SyncStatus.local,
          lastModified: DateTime.now(),
        ),
      ];

      when(mockLocalDataSource.getEntitiesNeedingSync())
          .thenAnswer((_) async => entities);
      when(mockLocalDataSource.updateEntitySyncStatus(any, any, any))
          .thenAnswer((_) async {});
      when(mockHazardRepository.getHazardById('hazard1'))
          .thenAnswer((_) async => testHazard);
      when(mockRemoteDataSource.uploadHazard(testHazard))
          .thenAnswer((_) async => false);

      // Act
      final result = await repository.syncToRemote();

      // Assert
      expect(result, false);
      verify(mockLocalDataSource.updateEntitySyncStatus(
        'hazard1',
        SyncEntityType.hazard,
        SyncStatus.error,
      )).called(1);
    });

    test('should sync from remote successfully', () async {
      // Arrange
      final remoteWalkabouts = [testWalkabout];
      final remoteHazards = [testHazard];

      when(mockWalkaboutRepository.getAllWalkabouts())
          .thenAnswer((_) async => [testWalkabout]);
      when(mockLocalDataSource.getLastSyncTime())
          .thenAnswer((_) async => null);
      when(mockRemoteDataSource.downloadWalkabouts('user1', null))
          .thenAnswer((_) async => remoteWalkabouts);
      when(mockRemoteDataSource.downloadHazards('walkabout1', null))
          .thenAnswer((_) async => remoteHazards);
      when(mockWalkaboutRepository.getWalkaboutById('walkabout1'))
          .thenAnswer((_) async => null);
      when(mockWalkaboutRepository.createWalkabout(any))
          .thenAnswer((_) async => testWalkabout);
      when(mockHazardRepository.getHazardById('hazard1'))
          .thenAnswer((_) async => null);
      when(mockHazardRepository.createHazard(any))
          .thenAnswer((_) async => testHazard);
      when(mockLocalDataSource.updateLastSyncTime(any))
          .thenAnswer((_) async {});

      // Act
      final result = await repository.syncFromRemote();

      // Assert
      expect(result, true);
      verify(mockWalkaboutRepository.createWalkabout(any)).called(1);
      verify(mockHazardRepository.createHazard(any)).called(1);
      verify(mockLocalDataSource.updateLastSyncTime(any)).called(1);
    });

    test('should perform full sync successfully', () async {
      // Arrange
      when(mockLocalDataSource.getEntitiesNeedingSync())
          .thenAnswer((_) async => []);
      when(mockWalkaboutRepository.getAllWalkabouts())
          .thenAnswer((_) async => [testWalkabout]);
      when(mockLocalDataSource.getLastSyncTime())
          .thenAnswer((_) async => null);
      when(mockRemoteDataSource.downloadWalkabouts('user1', null))
          .thenAnswer((_) async => []);
      when(mockRemoteDataSource.downloadHazards('walkabout1', null))
          .thenAnswer((_) async => []);
      when(mockLocalDataSource.updateLastSyncTime(any))
          .thenAnswer((_) async {});

      // Act
      final result = await repository.performFullSync();

      // Assert
      expect(result, true);
    });

    test('should get unresolved conflicts', () async {
      // Arrange
      final conflicts = [
        SyncConflict(
          id: 'conflict1',
          entityType: SyncEntityType.walkabout,
          entityId: 'walkabout1',
          localData: {'title': 'Local Title'},
          remoteData: {'title': 'Remote Title'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
      ];

      when(mockLocalDataSource.getUnresolvedConflicts())
          .thenAnswer((_) async => conflicts);

      // Act
      final result = await repository.getUnresolvedConflicts();

      // Assert
      expect(result, conflicts);
      verify(mockLocalDataSource.getUnresolvedConflicts()).called(1);
    });

    test('should resolve conflict successfully', () async {
      // Arrange
      when(mockLocalDataSource.resolveConflict('conflict1', SyncConflictResolution.useLocal))
          .thenAnswer((_) async {});

      // Act
      final result = await repository.resolveConflict('conflict1', SyncConflictResolution.useLocal);

      // Assert
      expect(result, true);
      verify(mockLocalDataSource.resolveConflict('conflict1', SyncConflictResolution.useLocal))
          .called(1);
    });

    test('should get sync statistics', () async {
      // Arrange
      final stats = {
        'walkabout_local': 2,
        'walkabout_synced': 3,
        'hazard_local': 1,
        'hazard_synced': 4,
        'conflicts': 1,
      };

      when(mockLocalDataSource.getSyncStatistics())
          .thenAnswer((_) async => stats);
      when(mockLocalDataSource.getLastSyncTime())
          .thenAnswer((_) async => DateTime.now());

      // Act
      final result = await repository.getSyncStatistics();

      // Assert
      expect(result.totalEntities, 10); // 2+3+1+4
      expect(result.syncedEntities, 7); // 3+4
      expect(result.pendingEntities, 3); // 2+1
      expect(result.conflictCount, 1);
      expect(result.lastSyncTime, isNotNull);
    });

    test('should prevent concurrent sync operations', () async {
      // Arrange
      when(mockLocalDataSource.getEntitiesNeedingSync())
          .thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: 100));
            return [];
          });

      // Act
      final future1 = repository.syncToRemote();
      final future2 = repository.syncToRemote();

      final results = await Future.wait([future1, future2]);

      // Assert
      expect(results.where((r) => r == true).length, 1);
      expect(results.where((r) => r == false).length, 1);
    });

    test('should mark entity as synced', () async {
      // Arrange
      when(mockLocalDataSource.updateEntitySyncStatus(
        'entity1',
        SyncEntityType.walkabout,
        SyncStatus.synced,
      )).thenAnswer((_) async {});

      // Act
      await repository.markEntityAsSynced('entity1', SyncEntityType.walkabout);

      // Assert
      verify(mockLocalDataSource.updateEntitySyncStatus(
        'entity1',
        SyncEntityType.walkabout,
        SyncStatus.synced,
      )).called(1);
    });

    test('should mark entity as sync error', () async {
      // Arrange
      when(mockLocalDataSource.updateEntitySyncStatus(
        'entity1',
        SyncEntityType.hazard,
        SyncStatus.error,
      )).thenAnswer((_) async {});

      // Act
      await repository.markEntityAsSyncError('entity1', SyncEntityType.hazard, 'Network error');

      // Assert
      verify(mockLocalDataSource.updateEntitySyncStatus(
        'entity1',
        SyncEntityType.hazard,
        SyncStatus.error,
      )).called(1);
    });

    test('should get and update last sync time', () async {
      // Arrange
      final timestamp = DateTime.now();
      when(mockLocalDataSource.getLastSyncTime())
          .thenAnswer((_) async => timestamp);
      when(mockLocalDataSource.updateLastSyncTime(timestamp))
          .thenAnswer((_) async {});

      // Act
      final result = await repository.getLastSyncTime();
      await repository.updateLastSyncTime(timestamp);

      // Assert
      expect(result, timestamp);
      verify(mockLocalDataSource.getLastSyncTime()).called(1);
      verify(mockLocalDataSource.updateLastSyncTime(timestamp)).called(1);
    });

    test('should track sync progress', () async {
      // Arrange & Act
      final isInProgress1 = await repository.isSyncInProgress();
      
      // Start sync (this will set _isSyncInProgress to true)
      when(mockLocalDataSource.getEntitiesNeedingSync())
          .thenAnswer((_) async => []);
      final syncFuture = repository.syncToRemote();
      
      final isInProgress2 = await repository.isSyncInProgress();
      await syncFuture;
      final isInProgress3 = await repository.isSyncInProgress();

      // Assert
      expect(isInProgress1, false);
      expect(isInProgress2, false); // This might be false due to timing
      expect(isInProgress3, false);
    });

    test('should cancel sync', () async {
      // Act
      await repository.cancelSync();
      final isInProgress = await repository.isSyncInProgress();

      // Assert
      expect(isInProgress, false);
    });
  });
}
