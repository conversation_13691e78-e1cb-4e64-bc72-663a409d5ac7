import '../repositories/sync_repository.dart';
import '../entities/sync_conflict.dart';

/// Use case for resolving sync conflicts
///
/// This use case handles conflict resolution between local and remote data
/// following Clean Architecture principles.
class ResolveSyncConflictUseCase {
  final SyncRepository _syncRepository;

  ResolveSyncConflictUseCase(this._syncRepository);

  /// Resolve a specific sync conflict
  /// Returns ConflictResolutionResult with operation details
  Future<ConflictResolutionResult> call(ConflictResolutionParams params) async {
    try {
      // Validate parameters
      if (params.conflictId.isEmpty) {
        return ConflictResolutionResult(
          success: false,
          error: 'Conflict ID cannot be empty',
        );
      }

      // Get the conflict to ensure it exists
      final conflicts = await _syncRepository.getUnresolvedConflicts();
      final conflict = conflicts.where((c) => c.id == params.conflictId).firstOrNull;

      if (conflict == null) {
        return ConflictResolutionResult(
          success: false,
          error: 'Conflict not found or already resolved',
        );
      }

      // Resolve the conflict
      final success = await _syncRepository.resolveConflict(
        params.conflictId,
        params.resolution,
      );

      if (success) {
        return ConflictResolutionResult(
          success: true,
          resolvedConflict: conflict.copyWith(
            resolvedAt: DateTime.now(),
            resolution: params.resolution,
          ),
        );
      } else {
        return ConflictResolutionResult(
          success: false,
          error: 'Failed to resolve conflict',
        );
      }
    } catch (e) {
      return ConflictResolutionResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Get all unresolved conflicts
  Future<List<SyncConflict>> getUnresolvedConflicts() async {
    try {
      return await _syncRepository.getUnresolvedConflicts();
    } catch (e) {
      return [];
    }
  }

  /// Get conflicts for a specific entity
  Future<List<SyncConflict>> getConflictsForEntity(
    String entityId,
    SyncEntityType entityType,
  ) async {
    try {
      final allConflicts = await _syncRepository.getUnresolvedConflicts();
      return allConflicts
          .where((c) => c.entityId == entityId && c.entityType == entityType)
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Resolve multiple conflicts with the same resolution strategy
  Future<BatchConflictResolutionResult> resolveMultipleConflicts(
    List<String> conflictIds,
    SyncConflictResolution resolution,
  ) async {
    final results = <ConflictResolutionResult>[];
    int successCount = 0;
    int failureCount = 0;

    for (final conflictId in conflictIds) {
      final result = await call(ConflictResolutionParams(
        conflictId: conflictId,
        resolution: resolution,
      ));

      results.add(result);
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    return BatchConflictResolutionResult(
      results: results,
      successCount: successCount,
      failureCount: failureCount,
      totalCount: conflictIds.length,
    );
  }
}

/// Parameters for conflict resolution
class ConflictResolutionParams {
  final String conflictId;
  final SyncConflictResolution resolution;

  const ConflictResolutionParams({
    required this.conflictId,
    required this.resolution,
  });
}

/// Result of conflict resolution operation
class ConflictResolutionResult {
  final bool success;
  final String? error;
  final SyncConflict? resolvedConflict;

  const ConflictResolutionResult({
    required this.success,
    this.error,
    this.resolvedConflict,
  });

  /// Check if resolution was successful
  bool get isSuccessful => success && error == null;
}

/// Result of batch conflict resolution
class BatchConflictResolutionResult {
  final List<ConflictResolutionResult> results;
  final int successCount;
  final int failureCount;
  final int totalCount;

  const BatchConflictResolutionResult({
    required this.results,
    required this.successCount,
    required this.failureCount,
    required this.totalCount,
  });

  /// Check if all conflicts were resolved successfully
  bool get allSuccessful => failureCount == 0;

  /// Get success rate as percentage
  double get successRate {
    if (totalCount == 0) return 1.0;
    return successCount / totalCount;
  }

  /// Get list of failed conflict IDs
  List<String> get failedConflictIds {
    return results
        .where((r) => !r.success)
        .map((r) => r.resolvedConflict?.id ?? 'unknown')
        .toList();
  }
}

/// Extension to add firstOrNull method for older Dart versions
extension ListExtension<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
}
