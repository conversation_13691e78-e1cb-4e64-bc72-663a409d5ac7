import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/repositories/sync_repository.dart';
import 'package:safestride/domain/usecases/sync_data.dart';
import 'package:safestride/domain/entities/sync_conflict.dart';

import 'sync_data_test.mocks.dart';

@GenerateMocks([SyncRepository])
void main() {
  late SyncDataUseCase useCase;
  late MockSyncRepository mockRepository;

  setUp(() {
    mockRepository = MockSyncRepository();
    useCase = SyncDataUseCase(mockRepository);
  });

  group('SyncDataUseCase', () {
    test('should perform full sync successfully', () async {
      // Arrange
      final mockStatistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 10,
        pendingEntities: 0,
        errorEntities: 0,
        conflictCount: 0,
        lastSyncTime: DateTime.now(),
      );

      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => false);
      when(mockRepository.performFullSync()).thenAnswer((_) async => true);
      when(mockRepository.getSyncStatistics()).thenAnswer((_) async => mockStatistics);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result.success, true);
      expect(result.error, null);
      expect(result.statistics, mockStatistics);
      verify(mockRepository.performFullSync()).called(1);
    });

    test('should return error when sync is already in progress', () async {
      // Arrange
      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => true);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result.success, false);
      expect(result.error, 'Sync already in progress');
      verifyNever(mockRepository.performFullSync());
    });

    test('should perform sync to remote only', () async {
      // Arrange
      final params = SyncParams(syncType: SyncType.toRemote);
      final mockStatistics = SyncStatistics(
        totalEntities: 5,
        syncedEntities: 5,
        pendingEntities: 0,
        errorEntities: 0,
        conflictCount: 0,
      );

      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => false);
      when(mockRepository.syncToRemote()).thenAnswer((_) async => true);
      when(mockRepository.getSyncStatistics()).thenAnswer((_) async => mockStatistics);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, true);
      verify(mockRepository.syncToRemote()).called(1);
      verifyNever(mockRepository.performFullSync());
      verifyNever(mockRepository.syncFromRemote());
    });

    test('should perform sync from remote only', () async {
      // Arrange
      final params = SyncParams(syncType: SyncType.fromRemote);
      final mockStatistics = SyncStatistics(
        totalEntities: 8,
        syncedEntities: 8,
        pendingEntities: 0,
        errorEntities: 0,
        conflictCount: 0,
      );

      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => false);
      when(mockRepository.syncFromRemote()).thenAnswer((_) async => true);
      when(mockRepository.getSyncStatistics()).thenAnswer((_) async => mockStatistics);

      // Act
      final result = await useCase.call(params);

      // Assert
      expect(result.success, true);
      verify(mockRepository.syncFromRemote()).called(1);
      verifyNever(mockRepository.performFullSync());
      verifyNever(mockRepository.syncToRemote());
    });

    test('should handle sync failure', () async {
      // Arrange
      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => false);
      when(mockRepository.performFullSync()).thenAnswer((_) async => false);
      when(mockRepository.getSyncStatistics()).thenAnswer((_) async => SyncStatistics(
        totalEntities: 10,
        syncedEntities: 5,
        pendingEntities: 3,
        errorEntities: 2,
        conflictCount: 1,
      ));

      // Act
      final result = await useCase.call();

      // Assert
      expect(result.success, false);
      expect(result.statistics?.errorEntities, 2);
    });

    test('should handle repository exception', () async {
      // Arrange
      when(mockRepository.isSyncInProgress()).thenThrow(Exception('Database error'));

      // Act
      final result = await useCase.call();

      // Assert
      expect(result.success, false);
      expect(result.error, contains('Database error'));
    });

    test('should get sync status successfully', () async {
      // Arrange
      final mockStatistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 8,
        pendingEntities: 1,
        errorEntities: 1,
        conflictCount: 2,
      );
      final mockConflicts = [
        SyncConflict(
          id: '1',
          entityType: SyncEntityType.walkabout,
          entityId: 'walkabout1',
          localData: {'title': 'Local Title'},
          remoteData: {'title': 'Remote Title'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
        SyncConflict(
          id: '2',
          entityType: SyncEntityType.hazard,
          entityId: 'hazard1',
          localData: {'severity': 'high'},
          remoteData: {'severity': 'medium'},
          conflictType: SyncConflictType.updateConflict,
          createdAt: DateTime.now(),
        ),
      ];

      when(mockRepository.getSyncStatistics()).thenAnswer((_) async => mockStatistics);
      when(mockRepository.isSyncInProgress()).thenAnswer((_) async => false);
      when(mockRepository.getUnresolvedConflicts()).thenAnswer((_) async => mockConflicts);

      // Act
      final result = await useCase.getSyncStatus();

      // Assert
      expect(result.statistics, mockStatistics);
      expect(result.isInProgress, false);
      expect(result.conflictCount, 2);
      expect(result.error, null);
    });

    test('should cancel sync successfully', () async {
      // Arrange
      when(mockRepository.cancelSync()).thenAnswer((_) async {});

      // Act
      final result = await useCase.cancelSync();

      // Assert
      expect(result, true);
      verify(mockRepository.cancelSync()).called(1);
    });

    test('should handle cancel sync failure', () async {
      // Arrange
      when(mockRepository.cancelSync()).thenThrow(Exception('Cancel failed'));

      // Act
      final result = await useCase.cancelSync();

      // Assert
      expect(result, false);
    });
  });

  group('SyncStatistics', () {
    test('should calculate sync progress correctly', () {
      // Arrange
      final statistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 7,
        pendingEntities: 2,
        errorEntities: 1,
        conflictCount: 0,
      );

      // Act & Assert
      expect(statistics.syncProgress, 0.7);
    });

    test('should return 1.0 progress when no entities', () {
      // Arrange
      final statistics = SyncStatistics(
        totalEntities: 0,
        syncedEntities: 0,
        pendingEntities: 0,
        errorEntities: 0,
        conflictCount: 0,
      );

      // Act & Assert
      expect(statistics.syncProgress, 1.0);
    });

    test('should detect fully synced state', () {
      // Arrange
      final statistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 10,
        pendingEntities: 0,
        errorEntities: 0,
        conflictCount: 0,
      );

      // Act & Assert
      expect(statistics.isFullySynced, true);
    });

    test('should detect sync issues', () {
      // Arrange
      final statistics = SyncStatistics(
        totalEntities: 10,
        syncedEntities: 8,
        pendingEntities: 0,
        errorEntities: 1,
        conflictCount: 1,
      );

      // Act & Assert
      expect(statistics.hasSyncIssues, true);
    });
  });
}
