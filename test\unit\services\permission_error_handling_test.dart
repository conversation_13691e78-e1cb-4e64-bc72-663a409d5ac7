import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'package:safestride/services/camera/camera_service.dart';
import 'package:safestride/services/location/location_service.dart';

import 'permission_error_handling_test.mocks.dart';

@GenerateMocks([ImagePicker, Geolocator])
void main() {
  group('Permission Error Handling Tests', () {
    late MockImagePicker mockImagePicker;
    late CameraServiceImpl cameraService;
    late LocationServiceImpl locationService;

    setUp(() {
      mockImagePicker = MockImagePicker();
      cameraService = CameraServiceImpl();
      locationService = LocationServiceImpl();
    });

    group('Camera Permission Error Handling', () {
      test('should handle camera permission denied gracefully', () async {
        // Arrange
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenThrow(Exception('Camera permission denied'));

        // Act & Assert
        expect(
          () => cameraService.capturePhoto(),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to capture photo'),
          )),
        );
      });

      test('should handle camera not available error', () async {
        // Arrange
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenThrow(Exception('Camera not available'));

        // Act & Assert
        expect(
          () => cameraService.capturePhoto(),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to capture photo'),
          )),
        );
      });

      test('should handle gallery permission denied gracefully', () async {
        // Arrange
        when(mockImagePicker.pickImage(source: ImageSource.gallery))
            .thenThrow(Exception('Gallery permission denied'));

        // Act & Assert
        expect(
          () => cameraService.selectFromGallery(),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to select photo from gallery'),
          )),
        );
      });

      test('should handle camera hardware failure', () async {
        // Arrange
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenThrow(Exception('Camera hardware error'));

        // Act & Assert
        expect(
          () => cameraService.capturePhoto(),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to capture photo'),
          )),
        );
      });

      test('should handle user cancellation gracefully', () async {
        // Arrange - User cancels camera operation
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenAnswer((_) async => null);

        // Act
        final result = await cameraService.capturePhoto();

        // Assert
        expect(result, isNull);
      });

      test('should handle storage permission denied for saving photos', () async {
        // This test verifies that storage permission errors are handled
        // when trying to save captured photos
        
        // Arrange
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenThrow(Exception('Storage permission denied'));

        // Act & Assert
        expect(
          () => cameraService.capturePhoto(),
          throwsA(isA<CameraException>().having(
            (e) => e.message,
            'message',
            contains('Failed to capture photo'),
          )),
        );
      });
    });

    group('Location Permission Error Handling', () {
      test('should handle location permission denied permanently', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.deniedForever);

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Location permissions are permanently denied'),
          )),
        );
      });

      test('should handle location permission denied temporarily', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.denied);
        when(Geolocator.requestPermission())
            .thenAnswer((_) async => LocationPermission.denied);

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Location permissions are denied'),
          )),
        );
      });

      test('should handle location services disabled', () async {
        // Arrange
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Location services are disabled'),
          )),
        );
      });

      test('should handle location timeout error', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => true);
        when(Geolocator.getCurrentPosition(
          desiredAccuracy: anyNamed('desiredAccuracy'),
          timeLimit: anyNamed('timeLimit'),
        )).thenThrow(Exception('Location request timed out'));

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Failed to get current location'),
          )),
        );
      });

      test('should handle GPS hardware unavailable', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => true);
        when(Geolocator.getCurrentPosition(
          desiredAccuracy: anyNamed('desiredAccuracy'),
          timeLimit: anyNamed('timeLimit'),
        )).thenThrow(Exception('GPS hardware not available'));

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Failed to get current location'),
          )),
        );
      });

      test('should handle location accuracy insufficient', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => true);
        when(Geolocator.getCurrentPosition(
          desiredAccuracy: anyNamed('desiredAccuracy'),
          timeLimit: anyNamed('timeLimit'),
        )).thenThrow(Exception('Location accuracy insufficient'));

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Failed to get current location'),
          )),
        );
      });

      test('should handle network location unavailable', () async {
        // Arrange
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => true);
        when(Geolocator.getCurrentPosition(
          desiredAccuracy: anyNamed('desiredAccuracy'),
          timeLimit: anyNamed('timeLimit'),
        )).thenThrow(Exception('Network location unavailable'));

        // Act & Assert
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>().having(
            (e) => e.message,
            'message',
            contains('Failed to get current location'),
          )),
        );
      });
    });

    group('Permission Recovery Scenarios', () {
      test('should retry camera operation after permission granted', () async {
        // Arrange - First call fails, second succeeds
        when(mockImagePicker.pickImage(source: ImageSource.camera))
            .thenThrow(Exception('Permission denied'))
            .thenAnswer((_) async => XFile('/path/to/image.jpg'));

        // Act - First attempt should fail
        expect(
          () => cameraService.capturePhoto(),
          throwsA(isA<CameraException>()),
        );

        // Second attempt should succeed (simulating permission granted)
        final result = await cameraService.capturePhoto();
        expect(result, isNotNull);
      });

      test('should retry location operation after permission granted', () async {
        // Arrange - First call fails, second succeeds
        when(Geolocator.checkPermission())
            .thenAnswer((_) async => LocationPermission.denied)
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.requestPermission())
            .thenAnswer((_) async => LocationPermission.always);
        when(Geolocator.isLocationServiceEnabled())
            .thenAnswer((_) async => true);
        when(Geolocator.getCurrentPosition(
          desiredAccuracy: anyNamed('desiredAccuracy'),
          timeLimit: anyNamed('timeLimit'),
        )).thenAnswer((_) async => Position(
          latitude: 37.7749,
          longitude: -122.4194,
          timestamp: DateTime.now(),
          accuracy: 10.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        ));

        // Act - First attempt should fail
        expect(
          () => locationService.getCurrentLocation(),
          throwsA(isA<LocationException>()),
        );

        // Second attempt should succeed (simulating permission granted)
        final result = await locationService.getCurrentLocation();
        expect(result, isNotNull);
        expect(result.latitude, equals(37.7749));
        expect(result.longitude, equals(-122.4194));
      });
    });

    group('Permission State Validation', () {
      test('should validate camera availability before operations', () async {
        // This test ensures that camera availability is checked
        // before attempting operations
        
        // The current implementation should handle this gracefully
        // by catching exceptions and wrapping them in CameraException
        expect(() => cameraService.capturePhoto(), returnsNormally);
      });

      test('should validate location services before operations', () async {
        // This test ensures that location services are checked
        // before attempting operations
        
        // The current implementation should handle this gracefully
        // by catching exceptions and wrapping them in LocationException
        expect(() => locationService.getCurrentLocation(), returnsNormally);
      });
    });

    group('User Experience Error Handling', () {
      test('should provide user-friendly camera error messages', () {
        // Test that camera exceptions provide helpful messages
        const exception = CameraException('Camera permission denied');
        expect(exception.message, contains('Camera'));
        expect(exception.toString(), contains('CameraException'));
      });

      test('should provide user-friendly location error messages', () {
        // Test that location exceptions provide helpful messages
        const exception = LocationException('Location services disabled');
        expect(exception.message, contains('Location'));
        expect(exception.toString(), contains('LocationException'));
      });
    });
  });
}
