import '../../domain/entities/hazard.dart';
import '../../domain/entities/walkabout.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/repositories/hazard_repository.dart';
import '../datasources/local/hazard_local_datasource.dart';

/// Implementation of HazardRepository interface
///
/// This implementation currently uses only local data source for offline-first approach.
/// Remote synchronization will be added in future stories.
class HazardRepositoryImpl implements HazardRepository {
  final HazardLocalDataSource localDataSource;

  const HazardRepositoryImpl({required this.localDataSource});

  @override
  Future<Hazard> createHazard(Hazard hazard) async {
    try {
      return await localDataSource.createHazard(hazard);
    } catch (e) {
      throw Exception('Failed to create hazard: ${e.toString()}');
    }
  }

  @override
  Future<Hazard?> getHazardById(String id) async {
    try {
      return await localDataSource.getHazardById(id);
    } catch (e) {
      throw Exception('Failed to get hazard by ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsByWalkaboutId(String walkaboutId) async {
    try {
      return await localDataSource.getHazardsByWalkaboutId(walkaboutId);
    } catch (e) {
      throw Exception('Failed to get hazards by walkabout ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsBySeverity(
    String walkaboutId,
    HazardSeverity severity,
  ) async {
    try {
      return await localDataSource.getHazardsBySeverity(walkaboutId, severity);
    } catch (e) {
      throw Exception('Failed to get hazards by severity: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsByCategory(
    String walkaboutId,
    HazardCategory category,
  ) async {
    try {
      return await localDataSource.getHazardsByCategory(walkaboutId, category);
    } catch (e) {
      throw Exception('Failed to get hazards by category: ${e.toString()}');
    }
  }

  @override
  Future<Hazard> updateHazard(Hazard hazard) async {
    try {
      return await localDataSource.updateHazard(hazard);
    } catch (e) {
      throw Exception('Failed to update hazard: ${e.toString()}');
    }
  }

  @override
  Future<bool> deleteHazard(String id) async {
    try {
      return await localDataSource.deleteHazard(id);
    } catch (e) {
      throw Exception('Failed to delete hazard: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsToSync() async {
    try {
      return await localDataSource.getHazardsToSync();
    } catch (e) {
      throw Exception('Failed to get hazards to sync: ${e.toString()}');
    }
  }

  @override
  Future<Hazard> updateSyncStatus(String id, SyncStatus syncStatus) async {
    try {
      return await localDataSource.updateSyncStatus(id, syncStatus);
    } catch (e) {
      throw Exception('Failed to update sync status: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> searchHazards(String walkaboutId, String query) async {
    try {
      return await localDataSource.searchHazards(walkaboutId, query);
    } catch (e) {
      throw Exception('Failed to search hazards: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsInArea(
    String walkaboutId,
    GeoPoint center,
    double radiusMeters,
  ) async {
    try {
      return await localDataSource.getHazardsInArea(
        walkaboutId,
        center,
        radiusMeters,
      );
    } catch (e) {
      throw Exception('Failed to get hazards in area: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getHazardStatistics(String walkaboutId) async {
    try {
      return await localDataSource.getHazardStatistics(walkaboutId);
    } catch (e) {
      throw Exception('Failed to get hazard statistics: ${e.toString()}');
    }
  }
}
