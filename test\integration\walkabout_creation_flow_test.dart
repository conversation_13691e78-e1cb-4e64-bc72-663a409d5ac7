import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:safestride/main.dart' as app;
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/providers/walkabout_provider.dart';
import 'package:safestride/domain/entities/walkabout.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Walkabout Creation Flow Integration Tests', () {
    /// Helper function to authenticate a test user
    Future<void> authenticateTestUser(WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Login with test credentials
      await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'testpassword123');
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle(const Duration(seconds: 5));
    }

    /// Helper function to navigate to walkabout creation screen
    Future<void> navigateToWalkaboutCreation(WidgetTester tester) async {
      // Look for walkabout creation navigation button/icon
      // This might be a FAB, menu item, or navigation button
      if (find.byIcon(Icons.add).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.add));
        await tester.pumpAndSettle();
      } else if (find.text('Create Walkabout').evaluate().isNotEmpty) {
        await tester.tap(find.text('Create Walkabout'));
        await tester.pumpAndSettle();
      } else {
        // Navigate through menu or other means
        // This depends on the actual app navigation structure
        await tester.tap(find.byIcon(Icons.menu));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Walkabouts'));
        await tester.pumpAndSettle();
        await tester.tap(find.byIcon(Icons.add));
        await tester.pumpAndSettle();
      }
    }

    testWidgets('complete walkabout creation flow - success scenario', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Verify we're on the walkabout creation screen
      expect(find.text('Create Walkabout'), findsOneWidget);
      expect(find.text('Walkabout Title *'), findsOneWidget);

      // Fill out the walkabout form
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Integration Test Walkabout',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Description'),
        'This is a test walkabout created during integration testing',
      );

      // Select a status if dropdown is available
      if (find.text('Status').evaluate().isNotEmpty) {
        await tester.tap(find.text('Draft'));
        await tester.pumpAndSettle();
      }

      // Submit the form
      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify successful creation
      // Should either navigate back to list or show success message
      expect(find.text('Integration Test Walkabout'), findsOneWidget);
      
      // Verify the walkabout appears in the provider
      final walkaboutProvider = tester.element(find.byType(MaterialApp)).read<WalkaboutProvider>();
      expect(walkaboutProvider.walkabouts.any((w) => w.title == 'Integration Test Walkabout'), isTrue);
    });

    testWidgets('walkabout creation form validation', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Test empty form submission
      await tester.tap(find.text('Create'));
      await tester.pump();

      // Should show validation error for required title
      expect(find.text('Title is required'), findsOneWidget);

      // Test title too long
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'a' * 101, // Exceeds 100 character limit
      );
      await tester.tap(find.text('Create'));
      await tester.pump();

      expect(find.text('Title cannot exceed 100 characters'), findsOneWidget);

      // Test valid title
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Valid Test Title',
      );
      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should not show validation errors
      expect(find.text('Title is required'), findsNothing);
      expect(find.text('Title cannot exceed 100 characters'), findsNothing);
    });

    testWidgets('walkabout creation error handling', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Fill out form with valid data
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Error Test Walkabout',
      );

      // Mock a database error scenario by creating a walkabout with invalid data
      // This test verifies error handling in the UI
      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Check if error handling works properly
      final walkaboutProvider = tester.element(find.byType(MaterialApp)).read<WalkaboutProvider>();
      
      // If there's an error, it should be displayed
      if (walkaboutProvider.error != null) {
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text(walkaboutProvider.error!), findsOneWidget);
        
        // Test error dismissal
        await tester.tap(find.byIcon(Icons.close));
        await tester.pump();
        
        expect(find.byIcon(Icons.error_outline), findsNothing);
      }
    });

    testWidgets('walkabout creation with location selection', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Fill out basic form
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Location Test Walkabout',
      );

      // Test location picker if available
      if (find.text('Select Location').evaluate().isNotEmpty) {
        await tester.tap(find.text('Select Location'));
        await tester.pumpAndSettle();

        // Mock location selection (this depends on the actual location picker implementation)
        // For now, just verify the location picker opens
        expect(find.text('Location'), findsOneWidget);

        // Navigate back to form
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();
      }

      // Submit the form
      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify creation succeeded
      expect(find.text('Location Test Walkabout'), findsOneWidget);
    });

    testWidgets('walkabout creation with template selection', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Fill out basic form
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Template Test Walkabout',
      );

      // Test template selection if available
      if (find.text('Select Template').evaluate().isNotEmpty) {
        await tester.tap(find.text('Select Template'));
        await tester.pumpAndSettle();

        // Select a template (this depends on available templates)
        if (find.text('Safety Inspection').evaluate().isNotEmpty) {
          await tester.tap(find.text('Safety Inspection'));
          await tester.pumpAndSettle();
        }
      }

      // Submit the form
      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify creation succeeded
      expect(find.text('Template Test Walkabout'), findsOneWidget);
    });

    testWidgets('walkabout creation persistence verification', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Create a walkabout
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Persistence Test Walkabout',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Description'),
        'Testing database persistence',
      );

      await tester.tap(find.text('Create'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Navigate away and back to verify persistence
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Navigate back to walkabout list
      await navigateToWalkaboutCreation(tester);
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify the walkabout still exists in the list
      expect(find.text('Persistence Test Walkabout'), findsOneWidget);

      // Verify it exists in the provider
      final walkaboutProvider = tester.element(find.byType(MaterialApp)).read<WalkaboutProvider>();
      final persistedWalkabout = walkaboutProvider.walkabouts
          .where((w) => w.title == 'Persistence Test Walkabout')
          .firstOrNull;
      
      expect(persistedWalkabout, isNotNull);
      expect(persistedWalkabout?.description, equals('Testing database persistence'));
      expect(persistedWalkabout?.status, equals(WalkaboutStatus.draft));
    });

    testWidgets('walkabout creation loading states', (WidgetTester tester) async {
      await authenticateTestUser(tester);
      await navigateToWalkaboutCreation(tester);

      // Fill out form
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Walkabout Title *'),
        'Loading Test Walkabout',
      );

      // Submit form and immediately check for loading indicator
      await tester.tap(find.text('Create'));
      await tester.pump(); // Don't settle to catch loading state

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Loading indicator should be gone
      expect(find.byType(CircularProgressIndicator), findsNothing);
    });
  });
}
