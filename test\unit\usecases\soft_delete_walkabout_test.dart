import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/usecases/soft_delete_walkabout.dart';
import 'package:safestride/domain/repositories/walkabout_repository.dart';

import 'create_walkabout_test.mocks.dart';

@GenerateMocks([WalkaboutRepository])
void main() {
  late SoftDeleteWalkaboutUseCase useCase;
  late MockWalkaboutRepository mockRepository;

  setUp(() {
    mockRepository = MockWalkaboutRepository();
    useCase = SoftDeleteWalkaboutUseCase(repository: mockRepository);
  });

  group('SoftDeleteWalkaboutUseCase', () {
    const testWalkaboutId = 'test-walkabout-id';

    test('should soft delete walkabout successfully', () async {
      // Arrange
      when(mockRepository.deleteWalkabout(testWalkaboutId))
          .thenAnswer((_) async => true);

      // Act
      final result = await useCase.call(testWalkaboutId);

      // Assert
      expect(result, isTrue);
      verify(mockRepository.deleteWalkabout(testWalkaboutId)).called(1);
    });

    test('should return false when walkabout not found', () async {
      // Arrange
      when(mockRepository.deleteWalkabout(testWalkaboutId))
          .thenAnswer((_) async => false);

      // Act
      final result = await useCase.call(testWalkaboutId);

      // Assert
      expect(result, isFalse);
      verify(mockRepository.deleteWalkabout(testWalkaboutId)).called(1);
    });

    test('should throw ArgumentError when walkabout ID is empty', () async {
      // Act & Assert
      expect(
        () => useCase.call(''),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Walkabout ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.deleteWalkabout(any));
    });

    test('should throw ArgumentError when walkabout ID is whitespace', () async {
      // Act & Assert
      expect(
        () => useCase.call('   '),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Walkabout ID cannot be empty',
        )),
      );
      
      verifyNever(mockRepository.deleteWalkabout(any));
    });

    test('should propagate repository exceptions', () async {
      // Arrange
      when(mockRepository.deleteWalkabout(testWalkaboutId))
          .thenThrow(Exception('Database error'));

      // Act & Assert
      expect(
        () => useCase.call(testWalkaboutId),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Database error'),
        )),
      );
      
      verify(mockRepository.deleteWalkabout(testWalkaboutId)).called(1);
    });
  });
}
