import '../entities/walkabout.dart';
import '../repositories/walkabout_repository.dart';

/// Use case for retrieving soft-deleted walkabouts
/// 
/// This use case handles the business logic for retrieving walkabouts
/// that have been soft deleted for a specific user.
class GetDeletedWalkaboutsUseCase {
  final WalkaboutRepository repository;

  const GetDeletedWalkaboutsUseCase({required this.repository});

  /// Get all soft-deleted walkabouts for a user
  /// 
  /// Takes a user ID and returns all soft-deleted walkabouts for that user
  /// Returns list of deleted walkabouts ordered by deletion date (newest first)
  /// Returns empty list if no deleted walkabouts found
  /// Throws [Exception] if retrieval fails
  Future<List<Walkabout>> call(String userId) async {
    // Validate input
    if (userId.trim().isEmpty) {
      throw ArgumentError('User ID cannot be empty');
    }

    // Get deleted walkabouts through repository
    return await repository.getDeletedWalkaboutsByUserId(userId);
  }
}
